(()=>{var e={};e.id=469,e.ids=[469],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},1877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},5319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},8419:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>d,routeModule:()=>p,tree:()=>o});var r=t(482),i=t(9108),a=t(2563),l=t.n(a),c=t(8300),n={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>c[e]);t.d(s,n);let o=["",{children:["services",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,9914)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\services\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,1008)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,4117)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,2793)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,8206)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\not-found.tsx"]}],d=["C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\services\\page.tsx"],x="/services/page",m={require:t,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/services/page",pathname:"/services",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},3362:(e,s,t)=>{Promise.resolve().then(t.bind(t,727))},7189:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(9224).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},7418:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(9224).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},6755:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(9224).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},727:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>N});var r=t(2295),i=t(3729),a=t(2254),l=t(783),c=t.n(l),n=t(1223),o=t.n(n),d=t(7418),x=t(9224);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let m=(0,x.Z)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]]),p=(0,x.Z)("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]]);var u=t(7189),g=t(6755),h=t(2401),y=t(8629),v=t(828),j=t(9200),b=t(5814),f=t(1448),_=t(6548);function N(){let[e,s]=(0,i.useState)([]),[t,l]=(0,i.useState)([]),[n,x]=(0,i.useState)(!0),[N,w]=(0,i.useState)(null),[C,k]=(0,i.useState)(""),[P,A]=(0,i.useState)("title"),[D,Z]=(0,i.useState)("grid"),[S,F]=(0,i.useState)(!1),q=(0,a.useRouter)(),{addToCart:H}=(0,j.useCart)(),{isAuthenticated:O}=(0,b.useAuth)(),{showToast:$}=(0,f.useToast)();(0,i.useEffect)(()=>{M()},[]),(0,i.useEffect)(()=>{t.length>0&&U()},[P]);let M=async()=>{x(!0);try{let[e,t]=await Promise.all([_.jv.getServices({ordering:P}),_.jv.getCategories({level:0})]);console.log("Services data:",e),console.log("Categories data:",t),e&&"object"==typeof e&&"results"in e&&Array.isArray(e.results)?s(e.results):Array.isArray(e)?s(e):(console.warn("Services data is not in expected format:",e),s([])),t&&"object"==typeof t&&"results"in t&&Array.isArray(t.results)?l(t.results):Array.isArray(t)?l(t):(console.warn("Categories data is not in expected format:",t),l([]))}catch(e){console.error("Failed to fetch services:",e),$({type:"error",title:"Failed to load services"}),l([{id:1,name:"Cleaning",slug:"cleaning",description:"Professional cleaning services",level:0,services_count:5,is_active:!0},{id:2,name:"Plumbing",slug:"plumbing",description:"Expert plumbing services",level:0,services_count:8,is_active:!0},{id:3,name:"AC Service",slug:"ac-service",description:"AC installation and repair",level:0,services_count:4,is_active:!0}]),s([{id:1,title:"Basic House Cleaning",slug:"basic-house-cleaning",description:"Complete house cleaning service including all rooms",base_price:"1500",current_price:"1200",discount_percentage:20,time_to_complete:"2-3 hours",category:1,category_name:"Cleaning",is_active:!0,created_at:"2024-01-01T00:00:00Z",updated_at:"2024-01-01T00:00:00Z"},{id:2,title:"Plumbing Repair",slug:"plumbing-repair",description:"Professional plumbing repair and maintenance",base_price:"800",current_price:"800",time_to_complete:"1-2 hours",category:2,category_name:"Plumbing",is_active:!0,created_at:"2024-01-01T00:00:00Z",updated_at:"2024-01-01T00:00:00Z"}])}finally{x(!1)}},U=async()=>{x(!0);try{let e={ordering:P};if(C){let s=t.find(e=>e.id.toString()===C);s&&(e.category_slug=s.slug)}console.log("Filter params:",e);let r=await _.jv.getServices(e);console.log("Filtered services data:",r),r&&"object"==typeof r&&"results"in r&&Array.isArray(r.results)?s(r.results):Array.isArray(r)?s(r):(console.warn("Services data is not in expected format:",r),s([]))}catch(e){console.error("Failed to apply filters:",e),$({type:"error",title:"Failed to apply filters"})}finally{x(!1)}},E=async e=>{if(!O){q.push(`/auth/login?redirect=${encodeURIComponent(window.location.pathname)}`);return}w(e.id);try{await H(e),$({type:"success",title:"Added to cart",message:e.title})}catch(e){$({type:"error",title:"Failed to add to cart",message:e.message})}finally{w(null)}};return n?r.jsx(y.Z,{children:r.jsx(v.h2,{message:"Loading services..."})}):r.jsx(y.Z,{children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"All Services"}),r.jsx("p",{className:"text-gray-600",children:"Discover our complete range of professional home services"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[r.jsx("div",{className:"lg:col-span-1",children:(0,r.jsxs)("div",{className:"card p-6 sticky top-24",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Filters"}),r.jsx("button",{onClick:()=>F(!S),className:"lg:hidden btn-outline",children:r.jsx(d.Z,{className:"h-4 w-4"})})]}),(0,r.jsxs)("div",{className:`space-y-6 ${S?"block":"hidden lg:block"}`,children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Category"}),(0,r.jsxs)("select",{value:C,onChange:e=>k(e.target.value),className:"input",children:[r.jsx("option",{value:"",children:"All Categories"}),t&&Array.isArray(t)&&t.map(e=>r.jsx("option",{value:e.id,children:e.name},e.id))]})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Sort By"}),(0,r.jsxs)("select",{value:P,onChange:e=>A(e.target.value),className:"input",children:[r.jsx("option",{value:"title",children:"Name (A-Z)"}),r.jsx("option",{value:"-title",children:"Name (Z-A)"}),r.jsx("option",{value:"base_price",children:"Price (Low to High)"}),r.jsx("option",{value:"-base_price",children:"Price (High to Low)"}),r.jsx("option",{value:"-created_at",children:"Newest First"}),r.jsx("option",{value:"created_at",children:"Oldest First"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx("button",{onClick:U,className:"w-full btn-primary",children:"Apply Filters"}),r.jsx("button",{onClick:()=>{k(""),A("title"),M()},className:"w-full btn-secondary",children:"Clear Filters"})]})]})]})}),(0,r.jsxs)("div",{className:"lg:col-span-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("p",{className:"text-gray-600",children:[e.length," service",1!==e.length?"s":""," found"]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("button",{onClick:()=>Z("grid"),className:`p-2 rounded ${"grid"===D?"bg-primary-100 text-primary-600":"text-gray-400 hover:text-gray-600"}`,children:r.jsx(m,{className:"h-4 w-4"})}),r.jsx("button",{onClick:()=>Z("list"),className:`p-2 rounded ${"list"===D?"bg-primary-100 text-primary-600":"text-gray-400 hover:text-gray-600"}`,children:r.jsx(p,{className:"h-4 w-4"})})]})]}),e.length>0?r.jsx("div",{className:"grid"===D?"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6":"space-y-6",children:e.map(e=>(0,r.jsxs)("div",{className:`card-hover overflow-hidden ${"list"===D?"flex":""}`,children:[(0,r.jsxs)("div",{className:`bg-gray-200 relative ${"list"===D?"w-48 h-32 flex-shrink-0":"h-48"}`,children:[e.image?r.jsx(o(),{src:e.image,alt:e.title,fill:!0,className:"object-cover"}):r.jsx("div",{className:"w-full h-full bg-primary-100 flex items-center justify-center",children:r.jsx("span",{className:"text-primary-600 font-bold text-2xl",children:e.title.charAt(0)})}),e.discount_percentage&&e.discount_percentage>0&&(0,r.jsxs)("div",{className:"absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded text-sm font-medium",children:[e.discount_percentage,"% OFF"]})]}),(0,r.jsxs)("div",{className:"p-6 flex-1",children:[r.jsx("div",{className:"mb-2",children:r.jsx("span",{className:"text-sm text-primary-600 font-medium",children:e.category_name})}),r.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2 line-clamp-2",children:e.title}),r.jsx("p",{className:"text-gray-600 text-sm mb-4 line-clamp-3",children:e.description}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 mb-4 text-sm text-gray-500",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx(u.Z,{className:"h-4 w-4 mr-1"}),e.time_to_complete]}),(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx(g.Z,{className:"h-4 w-4 mr-1 text-yellow-400"}),"4.8"]})]}),r.jsx("div",{className:"flex items-center justify-between mb-4",children:r.jsx("div",{children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("span",{className:"text-2xl font-bold text-gray-900",children:["₹",e.current_price]}),e.discount_price&&e.base_price!==e.current_price&&(0,r.jsxs)("span",{className:"text-lg text-gray-500 line-through",children:["₹",e.base_price]})]})})}),(0,r.jsxs)("div",{className:`flex space-x-2 ${"list"===D?"flex-col":""}`,children:[r.jsx(c(),{href:`/services/${e.slug}`,className:"flex-1 btn-outline text-center",children:"View Details"}),(0,r.jsxs)(v.fl,{onClick:()=>E(e),isLoading:N===e.id,className:"flex-1 btn-primary",children:[r.jsx(h.Z,{className:"h-4 w-4 mr-2"}),"Add to Cart"]})]})]})]},e.id))}):(0,r.jsxs)("div",{className:"text-center py-16",children:[r.jsx("div",{className:"w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-6",children:r.jsx("span",{className:"text-gray-400 text-2xl",children:"\uD83D\uDD27"})}),r.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"No services found"}),r.jsx("p",{className:"text-gray-600 mb-8",children:"Try adjusting your filters or browse our categories."}),r.jsx(c(),{href:"/",className:"btn-primary",children:"Browse Categories"})]})]})]})]})})}},9914:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>a,__esModule:()=>i,default:()=>l});let r=(0,t(6843).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\next_js_customer\src\app\services\page.tsx`),{__esModule:i,$$typeof:a}=r,l=r.default}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[638,265,681,410,629],()=>t(8419));module.exports=r})();