(()=>{var e={};e.id=374,e.ids=[374],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},1877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},5319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},8133:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>g,pages:()=>c,routeModule:()=>u,tree:()=>d});var o=t(482),r=t(9108),i=t(2563),l=t.n(i),n=t(8300),a={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>n[e]);t.d(s,a);let d=["",{children:["debug-api",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,7879)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\debug-api\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,1008)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,4117)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,2793)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,8206)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\not-found.tsx"]}],c=["C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\debug-api\\page.tsx"],g="/debug-api/page",m={require:t,loadChunk:()=>Promise.resolve()},u=new o.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/debug-api/page",pathname:"/debug-api",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},6429:(e,s,t)=>{Promise.resolve().then(t.bind(t,8919))},8919:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var o=t(2295),r=t(3729),i=t(6548),l=t(5814);function n(){let[e,s]=(0,r.useState)({}),[t,n]=(0,r.useState)({}),[a,d]=(0,r.useState)({mobile_number:"+919876543210",name:"Test Customer",otp:"123456"}),{user:c,login:g,logout:m,isAuthenticated:u}=(0,l.useAuth)(),b=(e,t)=>{s(s=>({...s,[e]:t}))},p=(e,s)=>{n(t=>({...t,[e]:s}))},x=async(e,s)=>{p(e,!0);try{let t=await s();b(e,{success:!0,data:t}),console.log(`${e} success:`,t)}catch(s){b(e,{success:!1,error:s.message,details:s.details,status:s.status}),console.error(`${e} error:`,s)}finally{p(e,!1)}},h=async()=>{let e="completeRegistrationFlow";p(e,!0);try{console.log("=== Starting Complete Registration Flow ==="),console.log("Step 1: Registering user...");let s=await i.iJ.registerMobile({mobile_number:a.mobile_number,name:a.name,user_type:"CUSTOMER"});console.log("Registration result:",s),console.log("Step 2: Sending OTP...");try{let e=await i.iJ.sendOTP(a.mobile_number);console.log("OTP send result:",e)}catch(e){console.log("OTP send failed (might be expected if already sent):",e)}console.log("Step 3: Verifying OTP...");let t=await i.iJ.verifyOTP(a.mobile_number,a.otp);console.log("OTP verification result:",t),console.log("Step 4: Logging in...");let o=await i.iJ.loginMobile(a.mobile_number,a.otp);console.log("Login result:",o),g(o),b(e,{success:!0,data:{register:s,verify:t,login:o,message:"Complete registration flow successful!"}})}catch(s){console.error("Registration flow error:",s),b(e,{success:!1,error:s.message,details:s.details,status:s.status})}finally{p(e,!1)}},y=async()=>{let e="completeLoginFlow";p(e,!0);try{console.log("=== Starting Complete Login Flow ==="),console.log("Step 1: Sending OTP...");let s=await i.iJ.sendOTP(a.mobile_number);console.log("OTP send result:",s),console.log("Step 2: Logging in with OTP...");let t=await i.iJ.loginMobile(a.mobile_number,a.otp);console.log("Login result:",t),g(t),b(e,{success:!0,data:{sendOTP:s,login:t,message:"Complete login flow successful!"}})}catch(s){console.error("Login flow error:",s),b(e,{success:!1,error:s.message,details:s.details,status:s.status})}finally{p(e,!1)}};return o.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,o.jsxs)("div",{className:"max-w-6xl mx-auto px-4",children:[o.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-8",children:"API Debug Console"}),(0,o.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-8",children:[o.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Current User Status"}),(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsxs)("p",{children:[o.jsx("strong",{children:"Authenticated:"})," ",u?"Yes":"No"]}),(0,o.jsxs)("p",{children:[o.jsx("strong",{children:"User:"})," ",c?JSON.stringify(c,null,2):"None"]})]})]}),(0,o.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-8",children:[o.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Test Data"}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,o.jsxs)("div",{children:[o.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Mobile Number"}),o.jsx("input",{type:"text",value:a.mobile_number,onChange:e=>d({...a,mobile_number:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md"})]}),(0,o.jsxs)("div",{children:[o.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name"}),o.jsx("input",{type:"text",value:a.name,onChange:e=>d({...a,name:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md"})]}),(0,o.jsxs)("div",{children:[o.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"OTP"}),o.jsx("input",{type:"text",value:a.otp,onChange:e=>d({...a,otp:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md"})]})]})]}),(0,o.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-8",children:[o.jsx("h2",{className:"text-xl font-semibold mb-4",children:"API Tests"}),(0,o.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[o.jsx("button",{onClick:()=>x("registerMobile",()=>i.iJ.registerMobile({mobile_number:a.mobile_number,name:a.name,user_type:"CUSTOMER"})),disabled:t.registerMobile,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50",children:t.registerMobile?"Loading...":"Register Mobile"}),o.jsx("button",{onClick:()=>x("sendOTP",()=>i.iJ.sendOTP(a.mobile_number)),disabled:t.sendOTP,className:"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50",children:t.sendOTP?"Loading...":"Send OTP"}),o.jsx("button",{onClick:()=>x("verifyOTP",()=>i.iJ.verifyOTP(a.mobile_number,a.otp)),disabled:t.verifyOTP,className:"px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 disabled:opacity-50",children:t.verifyOTP?"Loading...":"Verify OTP"}),o.jsx("button",{onClick:()=>x("loginMobile",()=>i.iJ.loginMobile(a.mobile_number,a.otp)),disabled:t.loginMobile,className:"px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50",children:t.loginMobile?"Loading...":"Login Mobile"}),o.jsx("button",{onClick:h,disabled:t.completeRegistrationFlow,className:"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 col-span-2",children:t.completeRegistrationFlow?"Loading...":"Complete Registration Flow"}),o.jsx("button",{onClick:y,disabled:t.completeLoginFlow,className:"px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50 col-span-2",children:t.completeLoginFlow?"Loading...":"Complete Login Flow"}),o.jsx("button",{onClick:()=>x("getCart",()=>i.wk.getCart()),disabled:t.getCart,className:"px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 disabled:opacity-50",children:t.getCart?"Loading...":"Get Cart"}),o.jsx("button",{onClick:()=>x("getProfile",()=>i.iJ.getProfile()),disabled:t.getProfile,className:"px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 disabled:opacity-50",children:t.getProfile?"Loading...":"Get Profile"}),o.jsx("button",{onClick:()=>{m(),b("logout",{success:!0,message:"Logged out successfully"})},className:"px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600",children:"Logout"})]})]}),(0,o.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[o.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Test Results"}),o.jsx("div",{className:"space-y-4",children:Object.entries(e).map(([e,s])=>(0,o.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,o.jsxs)("h3",{className:"font-semibold text-lg mb-2 flex items-center",children:[e,o.jsx("span",{className:`ml-2 px-2 py-1 rounded text-sm ${s.success?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:s.success?"SUCCESS":"ERROR"})]}),o.jsx("pre",{className:"bg-gray-100 p-3 rounded text-sm overflow-auto",children:JSON.stringify(s,null,2)})]},e))})]})]})})}},7879:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>i,__esModule:()=>r,default:()=>l});let o=(0,t(6843).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\next_js_customer\src\app\debug-api\page.tsx`),{__esModule:r,$$typeof:i}=o,l=o.default}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),o=s.X(0,[638,265,410],()=>t(8133));module.exports=o})();