"use strict";exports.id=629,exports.ids=[629],exports.modules={8629:(e,s,r)=>{r.d(s,{Z:()=>S});var a=r(2295),t=r(3729),l=r(783),i=r.n(l),c=r(2254),n=r(8765),x=r(2401),o=r(8822),h=r(1917),d=r(8120),m=r(4513),j=r(8200),g=r(5814),p=r(9200);let y=()=>{let[e,s]=(0,t.useState)(!1),[r,l]=(0,t.useState)(""),{user:y,isAuthenticated:N,logout:f}=(0,g.useAuth)(),{getTotalItems:v}=(0,p.useCart)(),u=(0,c.useRouter)(),b=e=>{e.preventDefault(),r.trim()&&(u.push(`/search?q=${encodeURIComponent(r.trim())}`),l(""))},w=async()=>{try{await f(),u.push("/")}catch(e){console.error("Logout error:",e)}},Z=v();return(0,a.jsxs)("header",{className:"bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40",children:[(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between h-16",children:[a.jsx("div",{className:"flex items-center",children:(0,a.jsxs)(i(),{href:"/",className:"flex items-center space-x-2",children:[a.jsx("div",{className:"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center",children:a.jsx("span",{className:"text-white font-bold text-sm",children:"HS"})}),a.jsx("span",{className:"text-xl font-bold text-gray-900 hidden sm:block",children:"Home Services"})]})}),a.jsx("div",{className:"hidden md:flex flex-1 max-w-lg mx-8",children:a.jsx("form",{onSubmit:b,className:"w-full",children:(0,a.jsxs)("div",{className:"relative",children:[a.jsx("input",{type:"text",value:r,onChange:e=>l(e.target.value),placeholder:"Search for services...",className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"}),a.jsx(n.Z,{className:"absolute left-3 top-2.5 h-5 w-5 text-gray-400"})]})})}),(0,a.jsxs)("div",{className:"hidden md:flex items-center space-x-4",children:[(0,a.jsxs)(i(),{href:"/cart",className:"relative p-2 text-gray-600 hover:text-primary-600 transition-colors",children:[a.jsx(x.Z,{className:"h-6 w-6"}),Z>0&&a.jsx("span",{className:"absolute -top-1 -right-1 bg-primary-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center",children:Z>99?"99+":Z})]}),N?(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsxs)("button",{className:"flex items-center space-x-2 p-2 text-gray-600 hover:text-primary-600 transition-colors",children:[a.jsx(o.Z,{className:"h-6 w-6"}),a.jsx("span",{className:"text-sm font-medium",children:y?.name})]}),a.jsx("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200",children:(0,a.jsxs)("div",{className:"py-2",children:[(0,a.jsxs)(i(),{href:"/profile",className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50",children:[a.jsx(o.Z,{className:"h-4 w-4 mr-3"}),"Profile"]}),(0,a.jsxs)(i(),{href:"/orders",className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50",children:[a.jsx(h.Z,{className:"h-4 w-4 mr-3"}),"My Orders"]}),(0,a.jsxs)("button",{onClick:w,className:"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50",children:[a.jsx(d.Z,{className:"h-4 w-4 mr-3"}),"Logout"]})]})})]}):a.jsx(i(),{href:"/auth/login",className:"btn-primary",children:"Login"})]}),a.jsx("div",{className:"md:hidden",children:a.jsx("button",{onClick:()=>s(!e),className:"p-2 text-gray-600 hover:text-primary-600",children:e?a.jsx(m.Z,{className:"h-6 w-6"}):a.jsx(j.Z,{className:"h-6 w-6"})})})]}),a.jsx("div",{className:"md:hidden pb-4",children:a.jsx("form",{onSubmit:b,children:(0,a.jsxs)("div",{className:"relative",children:[a.jsx("input",{type:"text",value:r,onChange:e=>l(e.target.value),placeholder:"Search for services...",className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"}),a.jsx(n.Z,{className:"absolute left-3 top-2.5 h-5 w-5 text-gray-400"})]})})})]}),e&&a.jsx("div",{className:"md:hidden bg-white border-t border-gray-200",children:(0,a.jsxs)("div",{className:"px-4 py-2 space-y-2",children:[(0,a.jsxs)(i(),{href:"/cart",className:"flex items-center justify-between p-3 text-gray-700 hover:bg-gray-50 rounded-lg",onClick:()=>s(!1),children:[(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx(x.Z,{className:"h-5 w-5 mr-3"}),"Cart"]}),Z>0&&a.jsx("span",{className:"bg-primary-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center",children:Z>99?"99+":Z})]}),N?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(i(),{href:"/profile",className:"flex items-center p-3 text-gray-700 hover:bg-gray-50 rounded-lg",onClick:()=>s(!1),children:[a.jsx(o.Z,{className:"h-5 w-5 mr-3"}),"Profile"]}),(0,a.jsxs)(i(),{href:"/orders",className:"flex items-center p-3 text-gray-700 hover:bg-gray-50 rounded-lg",onClick:()=>s(!1),children:[a.jsx(h.Z,{className:"h-5 w-5 mr-3"}),"My Orders"]}),(0,a.jsxs)("button",{onClick:()=>{w(),s(!1)},className:"flex items-center w-full p-3 text-gray-700 hover:bg-gray-50 rounded-lg",children:[a.jsx(d.Z,{className:"h-5 w-5 mr-3"}),"Logout"]})]}):a.jsx(i(),{href:"/auth/login",className:"block p-3 text-center bg-primary-600 text-white rounded-lg hover:bg-primary-700",onClick:()=>s(!1),children:"Login"})]})})]})};var N=r(3592),f=r(5576),v=r(3632),u=r(626),b=r(1206),w=r(508);let Z=()=>a.jsx("footer",{className:"bg-gray-900 text-white",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("div",{className:"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center",children:a.jsx("span",{className:"text-white font-bold text-sm",children:"HS"})}),a.jsx("span",{className:"text-xl font-bold",children:"Home Services"})]}),a.jsx("p",{className:"text-gray-300 text-sm",children:"Professional home services at your doorstep. Quality, reliability, and convenience in every service."}),(0,a.jsxs)("div",{className:"flex space-x-4",children:[a.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:a.jsx(N.Z,{className:"h-5 w-5"})}),a.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:a.jsx(f.Z,{className:"h-5 w-5"})}),a.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:a.jsx(v.Z,{className:"h-5 w-5"})})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("h3",{className:"text-lg font-semibold",children:"Quick Links"}),(0,a.jsxs)("ul",{className:"space-y-2 text-sm",children:[a.jsx("li",{children:a.jsx(i(),{href:"/",className:"text-gray-300 hover:text-white transition-colors",children:"Home"})}),a.jsx("li",{children:a.jsx(i(),{href:"/services",className:"text-gray-300 hover:text-white transition-colors",children:"All Services"})}),a.jsx("li",{children:a.jsx(i(),{href:"/about",className:"text-gray-300 hover:text-white transition-colors",children:"About Us"})}),a.jsx("li",{children:a.jsx(i(),{href:"/contact",className:"text-gray-300 hover:text-white transition-colors",children:"Contact"})})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("h3",{className:"text-lg font-semibold",children:"Popular Services"}),(0,a.jsxs)("ul",{className:"space-y-2 text-sm",children:[a.jsx("li",{children:a.jsx(i(),{href:"/categories/home-cleaning",className:"text-gray-300 hover:text-white transition-colors",children:"Home Cleaning"})}),a.jsx("li",{children:a.jsx(i(),{href:"/categories/plumbing",className:"text-gray-300 hover:text-white transition-colors",children:"Plumbing"})}),a.jsx("li",{children:a.jsx(i(),{href:"/categories/electrical",className:"text-gray-300 hover:text-white transition-colors",children:"Electrical"})}),a.jsx("li",{children:a.jsx(i(),{href:"/categories/appliance-repair",className:"text-gray-300 hover:text-white transition-colors",children:"Appliance Repair"})})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("h3",{className:"text-lg font-semibold",children:"Contact Us"}),(0,a.jsxs)("div",{className:"space-y-3 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(u.Z,{className:"h-4 w-4 text-primary-400"}),a.jsx("span",{className:"text-gray-300",children:"+91 98765 43210"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(b.Z,{className:"h-4 w-4 text-primary-400"}),a.jsx("span",{className:"text-gray-300",children:"<EMAIL>"})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[a.jsx(w.Z,{className:"h-4 w-4 text-primary-400 mt-0.5"}),(0,a.jsxs)("span",{className:"text-gray-300",children:["123 Service Street,",a.jsx("br",{}),"Mumbai, Maharashtra 400001"]})]})]})]})]}),a.jsx("div",{className:"border-t border-gray-800 mt-8 pt-8",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0",children:[a.jsx("div",{className:"text-sm text-gray-400",children:"\xa9 2024 Home Services. All rights reserved."}),(0,a.jsxs)("div",{className:"flex space-x-6 text-sm",children:[a.jsx(i(),{href:"/privacy",className:"text-gray-400 hover:text-white transition-colors",children:"Privacy Policy"}),a.jsx(i(),{href:"/terms",className:"text-gray-400 hover:text-white transition-colors",children:"Terms of Service"}),a.jsx(i(),{href:"/refund",className:"text-gray-400 hover:text-white transition-colors",children:"Refund Policy"})]})]})})]})}),S=({children:e,showFooter:s=!0})=>(0,a.jsxs)("div",{className:"min-h-screen flex flex-col",children:[a.jsx(y,{}),a.jsx("main",{className:"flex-1",children:e}),s&&a.jsx(Z,{})]})}};