(()=>{var e={};e.id=961,e.ids=[961],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},1877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},5319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},5929:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>c.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>o});var r=t(482),a=t(9108),i=t(2563),c=t.n(i),l=t(8300),n={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);t.d(s,n);let o=["",{children:["categories",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,8936)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\categories\\[slug]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,1008)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,4117)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,2793)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,8206)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\not-found.tsx"]}],d=["C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\categories\\[slug]\\page.tsx"],m="/categories/[slug]/page",x={require:t,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/categories/[slug]/page",pathname:"/categories/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},5324:(e,s,t)=>{Promise.resolve().then(t.bind(t,3105))},5299:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(9224).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},7751:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(9224).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},7189:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(9224).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},6755:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(9224).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},3105:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>b});var r=t(2295),a=t(3729),i=t(2254),c=t(783),l=t.n(c),n=t(1223),o=t.n(n),d=t(7751),m=t(5299),x=t(7189),p=t(6755),g=t(2401),u=t(8629),h=t(828),v=t(9200),y=t(5814),j=t(1448),f=t(6548);function b(){let[e,s]=(0,a.useState)(null),[t,c]=(0,a.useState)([]),[n,b]=(0,a.useState)(!0),[_,N]=(0,a.useState)(null),w=(0,i.useParams)(),P=(0,i.useRouter)(),{addToCart:C}=(0,v.useCart)(),{isAuthenticated:A}=(0,y.useAuth)(),{showToast:Z}=(0,j.useToast)(),D=w.slug;(0,a.useEffect)(()=>{(async()=>{try{let[e,t]=await Promise.all([f.jv.getCategoryDetail(D),f.jv.getCategoryServices(D)]);console.log("Category data:",e),console.log("Services data:",t),e?s(e):t&&"object"==typeof t&&"category"in t&&s(t.category),t&&"object"==typeof t?"services"in t&&Array.isArray(t.services)?(console.log("Found services array with length:",t.services.length),c(t.services)):"results"in t&&Array.isArray(t.results)?c(t.results):Array.isArray(t)?c(t):(console.warn("Services data is not in expected format:",t),c([])):Array.isArray(t)?c(t):(console.warn("Services data is not in expected format:",t),c([]))}catch(e){console.error("Failed to fetch category data:",e),Z({type:"error",title:"Failed to load category"}),D&&c([{id:1,title:`Sample Service for ${D}`,slug:`sample-service-${D}`,description:"This is a sample service for testing purposes",base_price:"1000",current_price:"800",discount_percentage:20,time_to_complete:"2 hours",category:1,category_name:D,is_active:!0,created_at:"2024-01-01T00:00:00Z",updated_at:"2024-01-01T00:00:00Z"}])}finally{b(!1)}})()},[D,Z]);let S=async e=>{if(!A){P.push(`/auth/login?redirect=${encodeURIComponent(window.location.pathname)}`);return}N(e.id);try{await C(e),Z({type:"success",title:"Added to cart",message:e.title})}catch(e){Z({type:"error",title:"Failed to add to cart",message:e.message})}finally{N(null)}};return n?r.jsx(u.Z,{children:r.jsx(h.h2,{message:"Loading category..."})}):e?r.jsx(u.Z,{children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("nav",{className:"flex items-center space-x-2 text-sm text-gray-600 mb-8",children:[r.jsx(l(),{href:"/",className:"hover:text-primary-600",children:"Home"}),r.jsx(d.Z,{className:"h-4 w-4"}),r.jsx("span",{className:"text-gray-900 font-medium",children:e.name})]}),r.jsx("div",{className:"mb-12",children:(0,r.jsxs)("div",{className:"flex items-center space-x-6",children:[e.image&&r.jsx("div",{className:"w-24 h-24 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0",children:r.jsx(o(),{src:e.image,alt:e.name,width:96,height:96,className:"w-full h-full object-cover"})}),(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-4xl font-bold text-gray-900 mb-2",children:e.name}),r.jsx("p",{className:"text-lg text-gray-600 mb-4",children:e.description}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:[e.services_count," services available"]})]})]})}),e.children&&e.children.length>0&&(0,r.jsxs)("div",{className:"mb-12",children:[r.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Browse by Category"}),r.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4",children:e.children.map(e=>(0,r.jsxs)(l(),{href:`/categories/${e.slug}`,className:"card-hover p-4 text-center group",children:[r.jsx("h3",{className:"font-semibold text-gray-900 mb-2 group-hover:text-primary-600",children:e.name}),(0,r.jsxs)("div",{className:"flex items-center justify-center text-primary-600 group-hover:text-primary-700",children:[r.jsx("span",{className:"text-sm",children:"View Services"}),r.jsx(m.Z,{className:"h-4 w-4 ml-1"})]})]},e.id))})]}),(0,r.jsxs)("div",{children:[r.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Available Services"}),t.length>0?r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:t.map(e=>(0,r.jsxs)("div",{className:"card-hover overflow-hidden",children:[(0,r.jsxs)("div",{className:"h-48 bg-gray-200 relative",children:[e.image?r.jsx(o(),{src:e.image,alt:e.title,fill:!0,className:"object-cover"}):r.jsx("div",{className:"w-full h-full bg-primary-100 flex items-center justify-center",children:r.jsx("span",{className:"text-primary-600 font-bold text-2xl",children:e.title.charAt(0)})}),e.discount_percentage&&e.discount_percentage>0&&(0,r.jsxs)("div",{className:"absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded text-sm font-medium",children:[e.discount_percentage,"% OFF"]})]}),(0,r.jsxs)("div",{className:"p-6",children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2 line-clamp-2",children:e.title}),r.jsx("p",{className:"text-gray-600 text-sm mb-4 line-clamp-3",children:e.description}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 mb-4 text-sm text-gray-500",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx(x.Z,{className:"h-4 w-4 mr-1"}),e.time_to_complete]}),(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx(p.Z,{className:"h-4 w-4 mr-1 text-yellow-400"}),"4.8"]})]}),r.jsx("div",{className:"flex items-center justify-between mb-4",children:r.jsx("div",{children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("span",{className:"text-2xl font-bold text-gray-900",children:["₹",e.current_price]}),e.discount_price&&e.base_price!==e.current_price&&(0,r.jsxs)("span",{className:"text-lg text-gray-500 line-through",children:["₹",e.base_price]})]})})}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[r.jsx(l(),{href:`/services/${e.slug}`,className:"flex-1 btn-outline text-center",children:"View Details"}),(0,r.jsxs)(h.fl,{onClick:()=>S(e),isLoading:_===e.id,className:"flex-1 btn-primary",children:[r.jsx(g.Z,{className:"h-4 w-4 mr-2"}),"Add to Cart"]})]})]})]},e.id))}):r.jsx("div",{className:"text-center py-12",children:r.jsx("p",{className:"text-gray-600",children:"No services available in this category."})})]})]})}):r.jsx(u.Z,{children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 text-center",children:[r.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Category not found"}),r.jsx(l(),{href:"/",className:"btn-primary",children:"Go Home"})]})})}},8936:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>i,__esModule:()=>a,default:()=>c});let r=(0,t(6843).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\next_js_customer\src\app\categories\[slug]\page.tsx`),{__esModule:a,$$typeof:i}=r,c=r.default}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[638,265,681,410,629],()=>t(5929));module.exports=r})();