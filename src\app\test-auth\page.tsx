'use client';

import React, { useState } from 'react';
import { authApi } from '@/lib/api';
import { useAuth } from '@/contexts/AuthContext';
import { LoginResponse } from '@/types/api';

export default function TestAuthPage() {
  const [results, setResults] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState<Record<string, boolean>>({});
  const [formData, setFormData] = useState({
    mobile_number: '+919876543210',
    name: 'Test Customer',
    otp: '123456',
  });

  const { user, login, logout } = useAuth();

  const addResult = (key: string, result: any) => {
    setResults(prev => ({ ...prev, [key]: result }));
  };

  const setLoadingState = (key: string, isLoading: boolean) => {
    setLoading(prev => ({ ...prev, [key]: isLoading }));
  };

  const testRegisterMobile = async () => {
    const key = 'registerMobile';
    setLoadingState(key, true);
    try {
      const result = await authApi.registerMobile({
        mobile_number: formData.mobile_number,
        name: formData.name,
        user_type: 'CUSTOMER',
      });
      addResult(key, { success: true, data: result });
    } catch (error: any) {
      addResult(key, { success: false, error: error.message, details: error.details });
    } finally {
      setLoadingState(key, false);
    }
  };

  const testSendOTP = async () => {
    const key = 'sendOTP';
    setLoadingState(key, true);
    try {
      const result = await authApi.sendOTP(formData.mobile_number);
      addResult(key, { success: true, data: result });
    } catch (error: any) {
      addResult(key, { success: false, error: error.message, details: error.details });
    } finally {
      setLoadingState(key, false);
    }
  };

  const testResendOTP = async () => {
    const key = 'resendOTP';
    setLoadingState(key, true);
    try {
      const result = await authApi.resendOTP(formData.mobile_number);
      addResult(key, { success: true, data: result });
    } catch (error: any) {
      addResult(key, { success: false, error: error.message, details: error.details });
    } finally {
      setLoadingState(key, false);
    }
  };

  const testVerifyOTP = async () => {
    const key = 'verifyOTP';
    setLoadingState(key, true);
    try {
      const result = await authApi.verifyOTP(formData.mobile_number, formData.otp);
      addResult(key, { success: true, data: result });
    } catch (error: any) {
      addResult(key, { success: false, error: error.message, details: error.details });
    } finally {
      setLoadingState(key, false);
    }
  };

  const testLoginMobile = async () => {
    const key = 'loginMobile';
    setLoadingState(key, true);
    try {
      const result = await authApi.loginMobile(formData.mobile_number, formData.otp);
      addResult(key, { success: true, data: result });
      // If login is successful, update the auth context
      if (result) {
        login(result as LoginResponse);
      }
    } catch (error: any) {
      addResult(key, { success: false, error: error.message, details: error.details });
    } finally {
      setLoadingState(key, false);
    }
  };

  const testGetProfile = async () => {
    const key = 'getProfile';
    setLoadingState(key, true);
    try {
      const result = await authApi.getProfile();
      addResult(key, { success: true, data: result });
    } catch (error: any) {
      addResult(key, { success: false, error: error.message, details: error.details });
    } finally {
      setLoadingState(key, false);
    }
  };

  const testLogout = async () => {
    const key = 'logout';
    setLoadingState(key, true);
    try {
      await logout();
      addResult(key, { success: true, message: 'Logged out successfully' });
    } catch (error: any) {
      addResult(key, { success: false, error: error.message, details: error.details });
    } finally {
      setLoadingState(key, false);
    }
  };

  const testCompleteRegistrationFlow = async () => {
    const key = 'completeRegistrationFlow';
    setLoadingState(key, true);
    try {
      // Step 1: Register
      const registerResult = await authApi.registerMobile({
        mobile_number: formData.mobile_number,
        name: formData.name,
        user_type: 'CUSTOMER',
      });

      // Step 2: Send OTP
      const otpResult = await authApi.sendOTP(formData.mobile_number);

      // Step 3: Verify OTP
      const verifyResult = await authApi.verifyOTP(formData.mobile_number, formData.otp);

      // Step 4: Login (this returns JWT tokens)
      const loginResult = await authApi.loginMobile(formData.mobile_number, formData.otp);

      // Step 5: Update auth context
      login(loginResult as LoginResponse);

      addResult(key, {
        success: true,
        data: {
          register: registerResult,
          sendOTP: otpResult,
          verifyOTP: verifyResult,
          login: loginResult,
          message: 'Complete registration flow successful with JWT tokens!'
        }
      });
    } catch (error: any) {
      addResult(key, { success: false, error: error.message, details: error.details });
    } finally {
      setLoadingState(key, false);
    }
  };

  const clearResults = () => {
    setResults({});
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Authentication API Test</h1>
        
        {/* Current User Status */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Current User Status</h2>
          {user ? (
            <div className="text-green-600">
              <p><strong>Logged in as:</strong> {user.name}</p>
              <p><strong>Mobile:</strong> {user.mobile_number}</p>
              <p><strong>User Type:</strong> {user.user_type}</p>
              <p><strong>Verified:</strong> {user.is_verified ? 'Yes' : 'No'}</p>
            </div>
          ) : (
            <p className="text-gray-600">Not logged in</p>
          )}
        </div>

        {/* Test Form */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Test Data</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Mobile Number
              </label>
              <input
                type="tel"
                value={formData.mobile_number}
                onChange={(e) => setFormData(prev => ({ ...prev, mobile_number: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Name
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                OTP
              </label>
              <input
                type="text"
                value={formData.otp}
                onChange={(e) => setFormData(prev => ({ ...prev, otp: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                maxLength={6}
              />
            </div>
          </div>
        </div>

        {/* Test Buttons */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">API Tests</h2>

          {/* Complete Flow Test */}
          <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <h3 className="font-semibold text-blue-800 mb-2">Complete Registration Flow (Recommended)</h3>
            <p className="text-sm text-blue-600 mb-3">
              This tests the complete registration flow: Register → Send OTP → Verify OTP → Auto Login with JWT tokens
            </p>
            <button
              onClick={testCompleteRegistrationFlow}
              disabled={loading.completeRegistrationFlow}
              className="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 font-semibold"
            >
              {loading.completeRegistrationFlow ? 'Running Complete Flow...' : 'Test Complete Registration Flow'}
            </button>
          </div>

          <h3 className="font-medium text-gray-700 mb-3">Individual API Tests</h3>
          <p className="text-sm text-gray-600 mb-4">
            <strong>Note:</strong> Individual "Verify OTP" test only verifies the OTP but doesn't return JWT tokens.
            Use "Login Mobile" after verification, or use the complete flow above.
          </p>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <button
              onClick={testRegisterMobile}
              disabled={loading.registerMobile}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {loading.registerMobile ? 'Loading...' : 'Register Mobile'}
            </button>
            
            <button
              onClick={testSendOTP}
              disabled={loading.sendOTP}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
            >
              {loading.sendOTP ? 'Loading...' : 'Send OTP'}
            </button>
            
            <button
              onClick={testResendOTP}
              disabled={loading.resendOTP}
              className="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 disabled:opacity-50"
            >
              {loading.resendOTP ? 'Loading...' : 'Resend OTP'}
            </button>
            
            <button
              onClick={testVerifyOTP}
              disabled={loading.verifyOTP}
              className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50"
            >
              {loading.verifyOTP ? 'Loading...' : 'Verify OTP'}
            </button>
            
            <button
              onClick={testLoginMobile}
              disabled={loading.loginMobile}
              className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50"
            >
              {loading.loginMobile ? 'Loading...' : 'Login Mobile'}
            </button>
            
            <button
              onClick={testGetProfile}
              disabled={loading.getProfile}
              className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 disabled:opacity-50"
            >
              {loading.getProfile ? 'Loading...' : 'Get Profile'}
            </button>
            
            <button
              onClick={testLogout}
              disabled={loading.logout}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
            >
              {loading.logout ? 'Loading...' : 'Logout'}
            </button>
            
            <button
              onClick={clearResults}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
            >
              Clear Results
            </button>
          </div>
        </div>

        {/* Results */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Test Results</h2>
          {Object.keys(results).length === 0 ? (
            <p className="text-gray-600">No test results yet. Click the buttons above to test the APIs.</p>
          ) : (
            <div className="space-y-4">
              {Object.entries(results).map(([key, result]) => (
                <div key={key} className="border rounded-lg p-4">
                  <h3 className="font-semibold text-lg mb-2 capitalize">{key}</h3>
                  <div className={`p-3 rounded ${result.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
                    <div className={`font-medium ${result.success ? 'text-green-800' : 'text-red-800'}`}>
                      {result.success ? '✅ Success' : '❌ Error'}
                    </div>
                    <pre className="mt-2 text-sm overflow-x-auto">
                      {JSON.stringify(result, null, 2)}
                    </pre>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
