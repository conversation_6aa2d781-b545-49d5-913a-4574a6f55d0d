'use client';

import React, { useState } from 'react';
import { authApi, cartApi } from '@/lib/api';
import { useAuth } from '@/contexts/AuthContext';
import { LoginResponse } from '@/types/api';

export default function DebugApiPage() {
  const [results, setResults] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState<Record<string, boolean>>({});
  const [formData, setFormData] = useState({
    mobile_number: '+919876543210',
    name: 'Test Customer',
    otp: '123456',
  });

  const { user, login, logout, isAuthenticated } = useAuth();

  const addResult = (key: string, result: any) => {
    setResults(prev => ({ ...prev, [key]: result }));
  };

  const setLoadingState = (key: string, isLoading: boolean) => {
    setLoading(prev => ({ ...prev, [key]: isLoading }));
  };

  const testApiCall = async (key: string, apiCall: () => Promise<any>) => {
    setLoadingState(key, true);
    try {
      const result = await apiCall();
      addResult(key, { success: true, data: result });
      console.log(`${key} success:`, result);
    } catch (error: any) {
      addResult(key, { success: false, error: error.message, details: error.details, status: error.status });
      console.error(`${key} error:`, error);
    } finally {
      setLoadingState(key, false);
    }
  };

  const testCompleteRegistrationFlow = async () => {
    const key = 'completeRegistrationFlow';
    setLoadingState(key, true);
    
    try {
      console.log('=== Starting Complete Registration Flow ===');
      
      // Step 1: Register user
      console.log('Step 1: Registering user...');
      const registerResult = await authApi.registerMobile({
        mobile_number: formData.mobile_number,
        name: formData.name,
        user_type: 'CUSTOMER',
      });
      console.log('Registration result:', registerResult);

      // Step 2: Send OTP (might be redundant if registration sends OTP)
      console.log('Step 2: Sending OTP...');
      try {
        const otpResult = await authApi.sendOTP(formData.mobile_number);
        console.log('OTP send result:', otpResult);
      } catch (otpError) {
        console.log('OTP send failed (might be expected if already sent):', otpError);
      }

      // Step 3: Verify OTP
      console.log('Step 3: Verifying OTP...');
      const verifyResult = await authApi.verifyOTP(formData.mobile_number, formData.otp);
      console.log('OTP verification result:', verifyResult);

      // Step 4: Login (this returns JWT tokens)
      console.log('Step 4: Logging in...');
      const loginResult = await authApi.loginMobile(formData.mobile_number, formData.otp);
      console.log('Login result:', loginResult);

      // Step 5: Update auth context
      login(loginResult as LoginResponse);

      addResult(key, {
        success: true,
        data: {
          register: registerResult,
          verify: verifyResult,
          login: loginResult,
          message: 'Complete registration flow successful!'
        }
      });
    } catch (error: any) {
      console.error('Registration flow error:', error);
      addResult(key, { success: false, error: error.message, details: error.details, status: error.status });
    } finally {
      setLoadingState(key, false);
    }
  };

  const testCompleteLoginFlow = async () => {
    const key = 'completeLoginFlow';
    setLoadingState(key, true);
    
    try {
      console.log('=== Starting Complete Login Flow ===');
      
      // Step 1: Send OTP
      console.log('Step 1: Sending OTP...');
      const otpResult = await authApi.sendOTP(formData.mobile_number);
      console.log('OTP send result:', otpResult);

      // Step 2: Login with OTP
      console.log('Step 2: Logging in with OTP...');
      const loginResult = await authApi.loginMobile(formData.mobile_number, formData.otp);
      console.log('Login result:', loginResult);

      // Step 3: Update auth context
      login(loginResult as LoginResponse);

      addResult(key, {
        success: true,
        data: {
          sendOTP: otpResult,
          login: loginResult,
          message: 'Complete login flow successful!'
        }
      });
    } catch (error: any) {
      console.error('Login flow error:', error);
      addResult(key, { success: false, error: error.message, details: error.details, status: error.status });
    } finally {
      setLoadingState(key, false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">API Debug Console</h1>
        
        {/* Current User Status */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Current User Status</h2>
          <div className="space-y-2">
            <p><strong>Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}</p>
            <p><strong>User:</strong> {user ? JSON.stringify(user, null, 2) : 'None'}</p>
          </div>
        </div>

        {/* Test Form Data */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Test Data</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Mobile Number</label>
              <input
                type="text"
                value={formData.mobile_number}
                onChange={(e) => setFormData({ ...formData, mobile_number: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">OTP</label>
              <input
                type="text"
                value={formData.otp}
                onChange={(e) => setFormData({ ...formData, otp: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>
          </div>
        </div>

        {/* Test Buttons */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">API Tests</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {/* Authentication Tests */}
            <button
              onClick={() => testApiCall('registerMobile', () => authApi.registerMobile({
                mobile_number: formData.mobile_number,
                name: formData.name,
                user_type: 'CUSTOMER',
              }))}
              disabled={loading.registerMobile}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {loading.registerMobile ? 'Loading...' : 'Register Mobile'}
            </button>

            <button
              onClick={() => testApiCall('sendOTP', () => authApi.sendOTP(formData.mobile_number))}
              disabled={loading.sendOTP}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
            >
              {loading.sendOTP ? 'Loading...' : 'Send OTP'}
            </button>

            <button
              onClick={() => testApiCall('verifyOTP', () => authApi.verifyOTP(formData.mobile_number, formData.otp))}
              disabled={loading.verifyOTP}
              className="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 disabled:opacity-50"
            >
              {loading.verifyOTP ? 'Loading...' : 'Verify OTP'}
            </button>

            <button
              onClick={() => testApiCall('loginMobile', () => authApi.loginMobile(formData.mobile_number, formData.otp))}
              disabled={loading.loginMobile}
              className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50"
            >
              {loading.loginMobile ? 'Loading...' : 'Login Mobile'}
            </button>

            {/* Complete Flow Tests */}
            <button
              onClick={testCompleteRegistrationFlow}
              disabled={loading.completeRegistrationFlow}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 col-span-2"
            >
              {loading.completeRegistrationFlow ? 'Loading...' : 'Complete Registration Flow'}
            </button>

            <button
              onClick={testCompleteLoginFlow}
              disabled={loading.completeLoginFlow}
              className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50 col-span-2"
            >
              {loading.completeLoginFlow ? 'Loading...' : 'Complete Login Flow'}
            </button>

            {/* Cart Tests */}
            <button
              onClick={() => testApiCall('getCart', () => cartApi.getCart())}
              disabled={loading.getCart}
              className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 disabled:opacity-50"
            >
              {loading.getCart ? 'Loading...' : 'Get Cart'}
            </button>

            <button
              onClick={() => testApiCall('getProfile', () => authApi.getProfile())}
              disabled={loading.getProfile}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 disabled:opacity-50"
            >
              {loading.getProfile ? 'Loading...' : 'Get Profile'}
            </button>

            <button
              onClick={() => {
                logout();
                addResult('logout', { success: true, message: 'Logged out successfully' });
              }}
              className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600"
            >
              Logout
            </button>
          </div>
        </div>

        {/* Results */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Test Results</h2>
          <div className="space-y-4">
            {Object.entries(results).map(([key, result]) => (
              <div key={key} className="border rounded-lg p-4">
                <h3 className="font-semibold text-lg mb-2 flex items-center">
                  {key}
                  <span className={`ml-2 px-2 py-1 rounded text-sm ${
                    result.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {result.success ? 'SUCCESS' : 'ERROR'}
                  </span>
                </h3>
                <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
                  {JSON.stringify(result, null, 2)}
                </pre>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
