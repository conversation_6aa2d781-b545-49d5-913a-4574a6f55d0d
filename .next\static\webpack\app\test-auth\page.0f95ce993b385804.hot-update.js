"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-auth/page",{

/***/ "(app-pages-browser)/./src/app/test-auth/page.tsx":
/*!************************************!*\
  !*** ./src/app/test-auth/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TestAuthPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction TestAuthPage() {\n    _s();\n    const [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        mobile_number: \"+919876543210\",\n        name: \"Test Customer\",\n        otp: \"123456\"\n    });\n    const { user, login, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const addResult = (key, result)=>{\n        setResults((prev)=>({\n                ...prev,\n                [key]: result\n            }));\n    };\n    const setLoadingState = (key, isLoading)=>{\n        setLoading((prev)=>({\n                ...prev,\n                [key]: isLoading\n            }));\n    };\n    const testRegisterMobile = async ()=>{\n        const key = \"registerMobile\";\n        setLoadingState(key, true);\n        try {\n            const result = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.registerMobile({\n                mobile_number: formData.mobile_number,\n                name: formData.name,\n                user_type: \"CUSTOMER\"\n            });\n            addResult(key, {\n                success: true,\n                data: result\n            });\n        } catch (error) {\n            addResult(key, {\n                success: false,\n                error: error.message,\n                details: error.details\n            });\n        } finally{\n            setLoadingState(key, false);\n        }\n    };\n    const testSendOTP = async ()=>{\n        const key = \"sendOTP\";\n        setLoadingState(key, true);\n        try {\n            const result = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.sendOTP(formData.mobile_number);\n            addResult(key, {\n                success: true,\n                data: result\n            });\n        } catch (error) {\n            addResult(key, {\n                success: false,\n                error: error.message,\n                details: error.details\n            });\n        } finally{\n            setLoadingState(key, false);\n        }\n    };\n    const testResendOTP = async ()=>{\n        const key = \"resendOTP\";\n        setLoadingState(key, true);\n        try {\n            const result = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.resendOTP(formData.mobile_number);\n            addResult(key, {\n                success: true,\n                data: result\n            });\n        } catch (error) {\n            addResult(key, {\n                success: false,\n                error: error.message,\n                details: error.details\n            });\n        } finally{\n            setLoadingState(key, false);\n        }\n    };\n    const testVerifyOTP = async ()=>{\n        const key = \"verifyOTP\";\n        setLoadingState(key, true);\n        try {\n            const result = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.verifyOTP(formData.mobile_number, formData.otp);\n            addResult(key, {\n                success: true,\n                data: result\n            });\n        } catch (error) {\n            addResult(key, {\n                success: false,\n                error: error.message,\n                details: error.details\n            });\n        } finally{\n            setLoadingState(key, false);\n        }\n    };\n    const testLoginMobile = async ()=>{\n        const key = \"loginMobile\";\n        setLoadingState(key, true);\n        try {\n            const result = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.loginMobile(formData.mobile_number, formData.otp);\n            addResult(key, {\n                success: true,\n                data: result\n            });\n            // If login is successful, update the auth context\n            if (result) {\n                login(result);\n            }\n        } catch (error) {\n            addResult(key, {\n                success: false,\n                error: error.message,\n                details: error.details\n            });\n        } finally{\n            setLoadingState(key, false);\n        }\n    };\n    const testGetProfile = async ()=>{\n        const key = \"getProfile\";\n        setLoadingState(key, true);\n        try {\n            const result = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.getProfile();\n            addResult(key, {\n                success: true,\n                data: result\n            });\n        } catch (error) {\n            addResult(key, {\n                success: false,\n                error: error.message,\n                details: error.details\n            });\n        } finally{\n            setLoadingState(key, false);\n        }\n    };\n    const testLogout = async ()=>{\n        const key = \"logout\";\n        setLoadingState(key, true);\n        try {\n            await logout();\n            addResult(key, {\n                success: true,\n                message: \"Logged out successfully\"\n            });\n        } catch (error) {\n            addResult(key, {\n                success: false,\n                error: error.message,\n                details: error.details\n            });\n        } finally{\n            setLoadingState(key, false);\n        }\n    };\n    const testCompleteRegistrationFlow = async ()=>{\n        const key = \"completeRegistrationFlow\";\n        setLoadingState(key, true);\n        try {\n            // Step 1: Register\n            const registerResult = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.registerMobile({\n                mobile_number: formData.mobile_number,\n                name: formData.name,\n                user_type: \"CUSTOMER\"\n            });\n            // Step 2: Send OTP\n            const otpResult = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.sendOTP(formData.mobile_number);\n            // Step 3: Verify OTP\n            const verifyResult = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.verifyOTP(formData.mobile_number, formData.otp);\n            // Step 4: Login (this returns JWT tokens)\n            const loginResult = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.loginMobile(formData.mobile_number, formData.otp);\n            // Step 5: Update auth context\n            login(loginResult);\n            addResult(key, {\n                success: true,\n                data: {\n                    register: registerResult,\n                    sendOTP: otpResult,\n                    verifyOTP: verifyResult,\n                    login: loginResult,\n                    message: \"Complete registration flow successful with JWT tokens!\"\n                }\n            });\n        } catch (error) {\n            addResult(key, {\n                success: false,\n                error: error.message,\n                details: error.details\n            });\n        } finally{\n            setLoadingState(key, false);\n        }\n    };\n    const clearResults = ()=>{\n        setResults({});\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-3xl font-bold text-gray-900 mb-8\",\n                    children: \"Authentication API Test\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-4\",\n                            children: \"Current User Status\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this),\n                        user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-green-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Logged in as:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 18\n                                        }, this),\n                                        \" \",\n                                        user.name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Mobile:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 18\n                                        }, this),\n                                        \" \",\n                                        user.mobile_number\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"User Type:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 18\n                                        }, this),\n                                        \" \",\n                                        user.user_type\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Verified:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 18\n                                        }, this),\n                                        \" \",\n                                        user.is_verified ? \"Yes\" : \"No\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Not logged in\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-4\",\n                            children: \"Test Data\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Mobile Number\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"tel\",\n                                            value: formData.mobile_number,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        mobile_number: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: formData.name,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        name: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"OTP\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: formData.otp,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        otp: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            maxLength: 6\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                    lineNumber: 191,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-4\",\n                            children: \"API Tests\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-blue-800 mb-2\",\n                                    children: \"Complete Registration Flow (Recommended)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-blue-600 mb-3\",\n                                    children: \"This tests the complete registration flow: Register → Send OTP → Verify OTP → Auto Login with JWT tokens\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: testCompleteRegistrationFlow,\n                                    disabled: loading.completeRegistrationFlow,\n                                    className: \"px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 font-semibold\",\n                                    children: loading.completeRegistrationFlow ? \"Running Complete Flow...\" : \"Test Complete Registration Flow\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-medium text-gray-700 mb-3\",\n                            children: \"Individual API Tests\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Note:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, this),\n                                ' Individual \"Verify OTP\" test only verifies the OTP but doesn\\'t return JWT tokens. Use \"Login Mobile\" after verification, or use the complete flow above.'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: testRegisterMobile,\n                                    disabled: loading.registerMobile,\n                                    className: \"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50\",\n                                    children: loading.registerMobile ? \"Loading...\" : \"Register Mobile\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: testSendOTP,\n                                    disabled: loading.sendOTP,\n                                    className: \"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50\",\n                                    children: loading.sendOTP ? \"Loading...\" : \"Send OTP\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: testResendOTP,\n                                    disabled: loading.resendOTP,\n                                    className: \"px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 disabled:opacity-50\",\n                                    children: loading.resendOTP ? \"Loading...\" : \"Resend OTP\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: testVerifyOTP,\n                                    disabled: loading.verifyOTP,\n                                    className: \"px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50\",\n                                    children: loading.verifyOTP ? \"Loading...\" : \"Verify OTP\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: testLoginMobile,\n                                    disabled: loading.loginMobile,\n                                    className: \"px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50\",\n                                    children: loading.loginMobile ? \"Loading...\" : \"Login Mobile\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: testGetProfile,\n                                    disabled: loading.getProfile,\n                                    className: \"px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 disabled:opacity-50\",\n                                    children: loading.getProfile ? \"Loading...\" : \"Get Profile\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: testLogout,\n                                    disabled: loading.logout,\n                                    className: \"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50\",\n                                    children: loading.logout ? \"Loading...\" : \"Logout\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: clearResults,\n                                    className: \"px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700\",\n                                    children: \"Clear Results\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-4\",\n                            children: \"Test Results\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 11\n                        }, this),\n                        Object.keys(results).length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"No test results yet. Click the buttons above to test the APIs.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: Object.entries(results).map((param)=>{\n                                let [key, result] = param;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border rounded-lg p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-lg mb-2 capitalize\",\n                                            children: key\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 rounded \".concat(result.success ? \"bg-green-50 border border-green-200\" : \"bg-red-50 border border-red-200\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium \".concat(result.success ? \"text-green-800\" : \"text-red-800\"),\n                                                    children: result.success ? \"✅ Success\" : \"❌ Error\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                    className: \"mt-2 text-sm overflow-x-auto\",\n                                                    children: JSON.stringify(result, null, 2)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, key, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 327,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n            lineNumber: 172,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\test-auth\\\\page.tsx\",\n        lineNumber: 171,\n        columnNumber: 5\n    }, this);\n}\n_s(TestAuthPage, \"RoiWEqD4N9caifiKwz854302idA=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = TestAuthPage;\nvar _c;\n$RefreshReg$(_c, \"TestAuthPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/test-auth/page.tsx\n"));

/***/ })

});