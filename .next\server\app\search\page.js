(()=>{var e={};e.id=797,e.ids=[797],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},1877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},5319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},6822:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>o});var r=t(482),a=t(9108),l=t(2563),i=t.n(l),c=t(8300),n={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>c[e]);t.d(s,n);let o=["",{children:["search",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,9473)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\search\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,1008)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,4117)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,2793)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,8206)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\not-found.tsx"]}],d=["C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\search\\page.tsx"],m="/search/page",x={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/search/page",pathname:"/search",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},2286:(e,s,t)=>{Promise.resolve().then(t.bind(t,8574))},7189:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(9224).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},7418:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(9224).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},6755:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(9224).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},8574:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>b});var r=t(2295),a=t(3729),l=t(2254),i=t(783),c=t.n(i),n=t(1223),o=t.n(n),d=t(8765),m=t(7418),x=t(7189),u=t(6755),p=t(2401),h=t(8629),g=t(828),y=t(9200),v=t(5814),j=t(1448),f=t(6548);function b(){let[e,s]=(0,a.useState)([]),[t,i]=(0,a.useState)([]),[n,b]=(0,a.useState)(!0),[N,_]=(0,a.useState)(null),[w,P]=(0,a.useState)(""),[C,S]=(0,a.useState)(""),[A,k]=(0,a.useState)({min:"",max:""}),[D,q]=(0,a.useState)(!1),Z=(0,l.useSearchParams)(),F=(0,l.useRouter)(),{addToCart:O}=(0,y.useCart)(),{isAuthenticated:U}=(0,v.useAuth)(),{showToast:H}=(0,j.useToast)(),$=Z.get("q")||"";(0,a.useEffect)(()=>{P($),M()},[$]);let M=async()=>{b(!0);try{let[e,t]=await Promise.all([f.jv.searchServices({q:$}),f.jv.getCategories({level:0})]);console.log("Search services data:",e),console.log("Search categories data:",t),e&&"object"==typeof e&&"results"in e&&Array.isArray(e.results)?s(e.results):Array.isArray(e)?s(e):(console.warn("Search services data is not in expected format:",e),s([])),t&&"object"==typeof t&&"results"in t&&Array.isArray(t.results)?i(t.results):Array.isArray(t)?i(t):(console.warn("Search categories data is not in expected format:",t),i([]))}catch(e){console.error("Failed to fetch search results:",e),H({type:"error",title:"Failed to load search results"})}finally{b(!1)}},E=async()=>{b(!0);try{let e={q:$};C&&(e.category=C),A.min&&(e.min_price=A.min),A.max&&(e.max_price=A.max);let t=await f.jv.searchServices(e);s(t)}catch(e){console.error("Failed to apply filters:",e),H({type:"error",title:"Failed to apply filters"})}finally{b(!1)}},R=async e=>{if(!U){F.push(`/auth/login?redirect=${encodeURIComponent(window.location.pathname)}`);return}_(e.id);try{await O(e),H({type:"success",title:"Added to cart",message:e.title})}catch(e){H({type:"error",title:"Failed to add to cart",message:e.message})}finally{_(null)}};return n?r.jsx(h.Z,{children:r.jsx(g.h2,{message:"Searching services..."})}):r.jsx(h.Z,{children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[r.jsx("form",{onSubmit:e=>{e.preventDefault(),w.trim()&&F.push(`/search?q=${encodeURIComponent(w.trim())}`)},className:"max-w-2xl",children:(0,r.jsxs)("div",{className:"relative",children:[r.jsx("input",{type:"text",value:w,onChange:e=>P(e.target.value),placeholder:"Search for services...",className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"}),r.jsx(d.Z,{className:"absolute left-3 top-3.5 h-5 w-5 text-gray-400"})]})}),$&&(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsxs)("h1",{className:"text-2xl font-bold text-gray-900",children:['Search results for "',$,'"']}),(0,r.jsxs)("p",{className:"text-gray-600 mt-1",children:[e.length," service",1!==e.length?"s":""," found"]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[r.jsx("div",{className:"lg:col-span-1",children:(0,r.jsxs)("div",{className:"card p-6 sticky top-24",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Filters"}),r.jsx("button",{onClick:()=>q(!D),className:"lg:hidden btn-outline",children:r.jsx(m.Z,{className:"h-4 w-4"})})]}),(0,r.jsxs)("div",{className:`space-y-6 ${D?"block":"hidden lg:block"}`,children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Category"}),(0,r.jsxs)("select",{value:C,onChange:e=>S(e.target.value),className:"input",children:[r.jsx("option",{value:"",children:"All Categories"}),t&&Array.isArray(t)&&t.map(e=>r.jsx("option",{value:e.id,children:e.name},e.id))]})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Price Range"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[r.jsx("input",{type:"number",value:A.min,onChange:e=>k({...A,min:e.target.value}),placeholder:"Min",className:"input"}),r.jsx("input",{type:"number",value:A.max,onChange:e=>k({...A,max:e.target.value}),placeholder:"Max",className:"input"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx("button",{onClick:E,className:"w-full btn-primary",children:"Apply Filters"}),r.jsx("button",{onClick:()=>{S(""),k({min:"",max:""}),M()},className:"w-full btn-secondary",children:"Clear Filters"})]})]})]})}),r.jsx("div",{className:"lg:col-span-3",children:e.length>0?r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6",children:e.map(e=>(0,r.jsxs)("div",{className:"card-hover overflow-hidden",children:[(0,r.jsxs)("div",{className:"h-48 bg-gray-200 relative",children:[e.image?r.jsx(o(),{src:e.image,alt:e.title,fill:!0,className:"object-cover"}):r.jsx("div",{className:"w-full h-full bg-primary-100 flex items-center justify-center",children:r.jsx("span",{className:"text-primary-600 font-bold text-2xl",children:e.title.charAt(0)})}),e.discount_percentage&&e.discount_percentage>0&&(0,r.jsxs)("div",{className:"absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded text-sm font-medium",children:[e.discount_percentage,"% OFF"]})]}),(0,r.jsxs)("div",{className:"p-6",children:[r.jsx("div",{className:"mb-2",children:r.jsx("span",{className:"text-sm text-primary-600 font-medium",children:e.category_name})}),r.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2 line-clamp-2",children:e.title}),r.jsx("p",{className:"text-gray-600 text-sm mb-4 line-clamp-3",children:e.description}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 mb-4 text-sm text-gray-500",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx(x.Z,{className:"h-4 w-4 mr-1"}),e.time_to_complete]}),(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx(u.Z,{className:"h-4 w-4 mr-1 text-yellow-400"}),"4.8"]})]}),r.jsx("div",{className:"flex items-center justify-between mb-4",children:r.jsx("div",{children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("span",{className:"text-2xl font-bold text-gray-900",children:["₹",e.current_price]}),e.discount_price&&e.base_price!==e.current_price&&(0,r.jsxs)("span",{className:"text-lg text-gray-500 line-through",children:["₹",e.base_price]})]})})}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[r.jsx(c(),{href:`/services/${e.slug}`,className:"flex-1 btn-outline text-center",children:"View Details"}),(0,r.jsxs)(g.fl,{onClick:()=>R(e),isLoading:N===e.id,className:"flex-1 btn-primary",children:[r.jsx(p.Z,{className:"h-4 w-4 mr-2"}),"Add to Cart"]})]})]})]},e.id))}):(0,r.jsxs)("div",{className:"text-center py-16",children:[r.jsx(d.Z,{className:"h-24 w-24 text-gray-300 mx-auto mb-6"}),r.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"No services found"}),r.jsx("p",{className:"text-gray-600 mb-8",children:$?`No services match your search for "${$}". Try different keywords or browse our categories.`:"Start searching for services to see results here."}),r.jsx(c(),{href:"/",className:"btn-primary",children:"Browse All Services"})]})})]})]})})}},9473:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>l,__esModule:()=>a,default:()=>i});let r=(0,t(6843).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\next_js_customer\src\app\search\page.tsx`),{__esModule:a,$$typeof:l}=r,i=r.default}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[638,265,681,410,629],()=>t(6822));module.exports=r})();