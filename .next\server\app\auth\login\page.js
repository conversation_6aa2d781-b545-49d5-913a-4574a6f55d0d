(()=>{var e={};e.id=716,e.ids=[716],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},1877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},5319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},7547:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>g,tree:()=>d});var r=s(482),i=s(9108),o=s(2563),n=s.n(o),a=s(8300),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);s.d(t,l);let d=["",{children:["auth",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,1904)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\auth\\login\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,1008)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,4117)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,2793)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,8206)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\not-found.tsx"]}],c=["C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\auth\\login\\page.tsx"],u="/auth/login/page",m={require:s,loadChunk:()=>Promise.resolve()},g=new r.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/auth/login/page",pathname:"/auth/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},5643:(e,t,s)=>{Promise.resolve().then(s.bind(s,7200))},2254:(e,t,s)=>{e.exports=s(4767)},7200:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p});var r=s(2295),i=s(3729),o=s(2254),n=s(783),a=s.n(n),l=s(3024),d=s(5814),c=s(1448),u=s(828),m=s(1034),g=s(6548),x=s(8263);function p(){let[e,t]=(0,i.useState)({mobile_number:"",otp:""}),[s,n]=(0,i.useState)(!1),[p,b]=(0,i.useState)(!1),[y,h]=(0,i.useState)(0),{login:f}=(0,d.useAuth)(),{showToast:j}=(0,c.useToast)(),v=(0,o.useRouter)(),P=(0,o.useSearchParams)(),_=P.get("redirect")||"/";(0,i.useEffect)(()=>{let e=P.get("mobile");e&&t(t=>({...t,mobile_number:e}))},[P]);let w=()=>{h(60);let e=setInterval(()=>{h(t=>t<=1?(clearInterval(e),0):t-1)},1e3)},T=async()=>{if(!e.mobile_number){j({type:"error",title:"Please enter mobile number"});return}n(!0);try{console.log("Sending OTP to:",e.mobile_number);let t=await g.iJ.sendOTP(e.mobile_number);console.log("OTP send response:",t),b(!0),w(),j({type:"success",title:"OTP sent successfully"})}catch(t){console.error("Send OTP error:",t),(0,x.Md)(t)?(j({type:"error",title:"Mobile number not registered",message:"This mobile number is not registered. Redirecting to signup page..."}),setTimeout(()=>{v.push(`/auth/register?mobile=${encodeURIComponent(e.mobile_number)}&from=login`)},2e3)):(0,x.l8)(t)?j({type:"warning",title:"Too many requests",message:"Too many OTP requests. Please wait for some time and try again."}):j({type:"error",title:"Failed to send OTP",message:(0,x.Mo)(t)})}finally{n(!1)}},N=async()=>{n(!0);try{console.log("Resending OTP to:",e.mobile_number),await g.iJ.resendOTP(e.mobile_number),w(),j({type:"success",title:"OTP resent successfully"})}catch(t){console.error("Resend OTP error:",t),(0,x.Md)(t)?(j({type:"error",title:"Mobile number not registered",message:"This mobile number is not registered. Redirecting to signup page..."}),setTimeout(()=>{v.push(`/auth/register?mobile=${encodeURIComponent(e.mobile_number)}&from=login`)},2e3)):(0,x.l8)(t)?j({type:"warning",title:"Too many requests",message:"Too many OTP requests. Please wait for some time and try again."}):j({type:"error",title:"Failed to resend OTP",message:(0,x.Mo)(t)})}finally{n(!1)}},C=async t=>{if(t.preventDefault(),!e.mobile_number||!e.otp){j({type:"error",title:"Please enter mobile number and OTP"});return}n(!0);try{console.log("Attempting login with:",e.mobile_number,"OTP length:",e.otp.length);let t=await g.iJ.loginMobile(e.mobile_number,e.otp);console.log("Login response:",t),f(t),j({type:"success",title:"Login successful"}),v.push(_)}catch(t){console.error("Login error:",t),(0,x.S9)(t)?j({type:"error",title:"Invalid OTP",message:(0,x.Mo)(t)}):(0,x.Md)(t)?(j({type:"error",title:"Mobile number not registered",message:"This mobile number is not registered. Redirecting to signup page..."}),setTimeout(()=>{v.push(`/auth/register?mobile=${encodeURIComponent(e.mobile_number)}&from=login`)},2e3)):j({type:"error",title:"Login failed",message:(0,x.Mo)(t)})}finally{n(!1)}};return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[(0,r.jsxs)(a(),{href:"/",className:"flex items-center justify-center mb-6",children:[r.jsx(l.Z,{className:"h-5 w-5 mr-2"}),"Back to Home"]}),r.jsx("div",{className:"flex justify-center mb-6",children:r.jsx("div",{className:"w-12 h-12 bg-primary-600 rounded-lg flex items-center justify-center",children:r.jsx("span",{className:"text-white font-bold text-lg",children:"HS"})})}),r.jsx("h2",{className:"text-center text-3xl font-bold text-gray-900",children:"Sign in to your account"}),(0,r.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",r.jsx(a(),{href:"/auth/register",className:"font-medium text-primary-600 hover:text-primary-500",children:"create a new account"})]})]}),(0,r.jsxs)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:[r.jsx(m.V,{type:"login"}),r.jsx("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:(0,r.jsxs)("form",{onSubmit:C,className:"space-y-6",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"mobile",className:"block text-sm font-medium text-gray-700",children:"Mobile Number"}),r.jsx("div",{className:"mt-1",children:r.jsx("input",{id:"mobile",name:"mobile",type:"tel",required:!0,value:e.mobile_number,onChange:s=>t({...e,mobile_number:s.target.value}),placeholder:"+91 98765 43210",className:"input"})})]}),p?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"otp",className:"block text-sm font-medium text-gray-700",children:"Enter OTP"}),r.jsx("div",{className:"mt-1",children:r.jsx("input",{id:"otp",name:"otp",type:"text",required:!0,value:e.otp,onChange:s=>t({...e,otp:s.target.value}),placeholder:"123456",className:"input",maxLength:6})})]}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[r.jsx(u.fl,{type:"submit",isLoading:s,className:"flex-1 btn-primary",children:"Verify & Login"}),0===y?r.jsx(u.fl,{type:"button",onClick:N,isLoading:s,className:"btn-outline",children:"Resend"}):(0,r.jsxs)("button",{type:"button",disabled:!0,className:"btn-outline opacity-50 cursor-not-allowed",children:[y,"s"]})]})]}):r.jsx(u.fl,{type:"button",onClick:T,isLoading:s,className:"w-full btn-primary",children:"Send OTP"})]})})]})]})}},1034:(e,t,s)=>{"use strict";s.d(t,{V:()=>d});var r=s(2295),i=s(3729),o=s(2254),n=s(9224);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,n.Z)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]),l=(0,n.Z)("LogIn",[["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}],["polyline",{points:"10 17 15 12 10 7",key:"1ail0h"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}]]),d=({type:e})=>{let[t,s]=(0,i.useState)(!1),[n,d]=(0,i.useState)(""),c=(0,o.useSearchParams)();return((0,i.useEffect)(()=>{let t=c.get("mobile"),r=c.get("from");if(t&&r){s(!0),"register"===e&&"login"===r?d(`Mobile number ${t} is not registered. Please create an account to continue.`):"login"===e&&"register"===r&&d(`Mobile number ${t} is already registered. Please login to continue.`);let i=setTimeout(()=>{s(!1)},1e4);return()=>clearTimeout(i)}},[c,e]),t)?r.jsx("div",{className:"mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[r.jsx("div",{className:"flex-shrink-0",children:"register"===e?r.jsx(a,{className:"h-5 w-5 text-blue-600"}):r.jsx(l,{className:"h-5 w-5 text-blue-600"})}),(0,r.jsxs)("div",{className:"flex-1",children:[r.jsx("h3",{className:"text-sm font-medium text-blue-900 mb-1",children:"register"===e?"Registration Required":"Login Required"}),r.jsx("p",{className:"text-sm text-blue-700",children:n})]}),(0,r.jsxs)("button",{onClick:()=>s(!1),className:"flex-shrink-0 text-blue-400 hover:text-blue-600",children:[r.jsx("span",{className:"sr-only",children:"Close"}),r.jsx("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})]})]})}):null}},8263:(e,t,s)=>{"use strict";s.d(t,{Md:()=>r,Mo:()=>a,RZ:()=>i,S9:()=>o,l8:()=>n});let r=e=>{if(400!==e.status)return!1;let t=e.message?.toLowerCase()||"",s=e.details?.detail?(Array.isArray(e.details.detail)?e.details.detail.join(" "):String(e.details.detail)).toLowerCase():"",r=e.details?.error?(Array.isArray(e.details.error)?e.details.error.join(" "):String(e.details.error)).toLowerCase():"";return["not registered","unregistered","mobile number not found","user not found","does not exist","no account found","please register"].some(e=>t.includes(e)||s.includes(e)||r.includes(e))},i=e=>{if(400!==e.status)return!1;let t=e.message?.toLowerCase()||"",s=e.details?.detail?(Array.isArray(e.details.detail)?e.details.detail.join(" "):String(e.details.detail)).toLowerCase():"",r=e.details?.error?(Array.isArray(e.details.error)?e.details.error.join(" "):String(e.details.error)).toLowerCase():"";return["already registered","already exists","mobile number already in use","user already exists","duplicate"].some(e=>t.includes(e)||s.includes(e)||r.includes(e))},o=e=>{if(400!==e.status)return!1;let t=e.message?.toLowerCase()||"",s=e.details?.detail?(Array.isArray(e.details.detail)?e.details.detail.join(" "):String(e.details.detail)).toLowerCase():"",r=e.details?.error?(Array.isArray(e.details.error)?e.details.error.join(" "):String(e.details.error)).toLowerCase():"";return["invalid otp","incorrect otp","otp expired","otp not found","wrong otp"].some(e=>t.includes(e)||s.includes(e)||r.includes(e))},n=e=>{if(429===e.status)return!0;let t=e.message?e.message.toLowerCase():"",s=e.details?.detail?(Array.isArray(e.details.detail)?e.details.detail.join(" "):String(e.details.detail)).toLowerCase():"",r=e.details?.error?(Array.isArray(e.details.error)?e.details.error.join(" "):String(e.details.error)).toLowerCase():"";return["too many requests","too many otp requests","rate limit","try again later","please wait","request limit exceeded","rate exceeded"].some(e=>t.includes(e)||s.includes(e)||r.includes(e))},a=e=>r(e)?"This mobile number is not registered. You will be redirected to create an account.":i(e)?"This mobile number is already registered. Please login instead.":o(e)?"Invalid or expired OTP. Please try again.":n(e)?"Too many OTP requests. Please wait for some time and try again.":e.message||"An unexpected error occurred. Please try again."},1904:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>o,__esModule:()=>i,default:()=>n});let r=(0,s(6843).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\next_js_customer\src\app\auth\login\page.tsx`),{__esModule:i,$$typeof:o}=r,n=r.default}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,265,410],()=>s(7547));module.exports=r})();