'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { Phone, ArrowLeft } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/components/ui/Toaster';
import { LoadingButton } from '@/components/ui/LoadingSpinner';
import { RedirectMessage } from '@/components/auth/RedirectMessage';
import { authApi } from '@/lib/api';
import { LoginResponse } from '@/types/api';
import { isUnregisteredMobileError, isInvalidOTPError, isRateLimitError, getUserFriendlyErrorMessage } from '@/lib/errorUtils';

export default function LoginPage() {
  // Customer app only uses mobile/OTP login
  const loginMethod = 'mobile';
  const [formData, setFormData] = useState({
    mobile_number: '',
    otp: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [otpSent, setOtpSent] = useState(false);
  const [otpTimer, setOtpTimer] = useState(0);

  const { login } = useAuth();
  const { showToast } = useToast();
  const router = useRouter();
  const searchParams = useSearchParams();
  const redirectTo = searchParams.get('redirect') || '/';

  // Pre-fill mobile number from URL parameter if available
  useEffect(() => {
    const mobileFromUrl = searchParams.get('mobile');
    if (mobileFromUrl) {
      setFormData(prev => ({ ...prev, mobile_number: mobileFromUrl }));
    }
  }, [searchParams]);

  // Start OTP timer
  const startOtpTimer = () => {
    setOtpTimer(60);
    const interval = setInterval(() => {
      setOtpTimer((prev) => {
        if (prev <= 1) {
          clearInterval(interval);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const handleSendOTP = async () => {
    if (!formData.mobile_number) {
      showToast({ type: 'error', title: 'Please enter mobile number' });
      return;
    }

    setIsLoading(true);
    try {
      console.log('Sending OTP to:', formData.mobile_number);
      const otpResponse = await authApi.sendOTP(formData.mobile_number);
      console.log('OTP send response:', otpResponse);

      setOtpSent(true);
      startOtpTimer();
      showToast({ type: 'success', title: 'OTP sent successfully' });
    } catch (error: any) {
      console.error('Send OTP error:', error);

      // Check if the error is due to unregistered mobile number
      if (isUnregisteredMobileError(error)) {
        showToast({
          type: 'error',
          title: 'Mobile number not registered',
          message: 'This mobile number is not registered. Redirecting to signup page...'
        });
        // Redirect to registration page after a short delay
        setTimeout(() => {
          router.push(`/auth/register?mobile=${encodeURIComponent(formData.mobile_number)}&from=login`);
        }, 2000);
      } else if (isRateLimitError(error)) {
        showToast({
          type: 'warning',
          title: 'Too many requests',
          message: 'Too many OTP requests. Please wait for some time and try again.'
        });
      } else {
        showToast({ type: 'error', title: 'Failed to send OTP', message: getUserFriendlyErrorMessage(error) });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendOTP = async () => {
    setIsLoading(true);
    try {
      console.log('Resending OTP to:', formData.mobile_number);
      await authApi.resendOTP(formData.mobile_number);
      startOtpTimer();
      showToast({ type: 'success', title: 'OTP resent successfully' });
    } catch (error: any) {
      console.error('Resend OTP error:', error);

      // Check if the error is due to unregistered mobile number
      if (isUnregisteredMobileError(error)) {
        showToast({
          type: 'error',
          title: 'Mobile number not registered',
          message: 'This mobile number is not registered. Redirecting to signup page...'
        });
        // Redirect to registration page after a short delay
        setTimeout(() => {
          router.push(`/auth/register?mobile=${encodeURIComponent(formData.mobile_number)}&from=login`);
        }, 2000);
      } else if (isRateLimitError(error)) {
        showToast({
          type: 'warning',
          title: 'Too many requests',
          message: 'Too many OTP requests. Please wait for some time and try again.'
        });
      } else {
        showToast({ type: 'error', title: 'Failed to resend OTP', message: getUserFriendlyErrorMessage(error) });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleMobileLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.mobile_number || !formData.otp) {
      showToast({ type: 'error', title: 'Please enter mobile number and OTP' });
      return;
    }

    setIsLoading(true);
    try {
      console.log('Attempting login with:', formData.mobile_number, 'OTP length:', formData.otp.length);

      const response = await authApi.loginMobile(formData.mobile_number, formData.otp);
      console.log('Login response:', response);

      login(response as LoginResponse);
      showToast({ type: 'success', title: 'Login successful' });
      router.push(redirectTo);
    } catch (error: any) {
      console.error('Login error:', error);

      if (isInvalidOTPError(error)) {
        showToast({ type: 'error', title: 'Invalid OTP', message: getUserFriendlyErrorMessage(error) });
      } else if (isUnregisteredMobileError(error)) {
        showToast({
          type: 'error',
          title: 'Mobile number not registered',
          message: 'This mobile number is not registered. Redirecting to signup page...'
        });
        setTimeout(() => {
          router.push(`/auth/register?mobile=${encodeURIComponent(formData.mobile_number)}&from=login`);
        }, 2000);
      } else {
        showToast({ type: 'error', title: 'Login failed', message: getUserFriendlyErrorMessage(error) });
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Customer app only uses mobile/OTP login - email login removed

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <Link href="/" className="flex items-center justify-center mb-6">
          <ArrowLeft className="h-5 w-5 mr-2" />
          Back to Home
        </Link>
        
        <div className="flex justify-center mb-6">
          <div className="w-12 h-12 bg-primary-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-lg">HS</span>
          </div>
        </div>
        
        <h2 className="text-center text-3xl font-bold text-gray-900">
          Sign in to your account
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          Or{' '}
          <Link href="/auth/register" className="font-medium text-primary-600 hover:text-primary-500">
            create a new account
          </Link>
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <RedirectMessage type="login" />
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          {/* Mobile Login Form - Customer app only uses mobile/OTP */}
            <form onSubmit={handleMobileLogin} className="space-y-6">
              <div>
                <label htmlFor="mobile" className="block text-sm font-medium text-gray-700">
                  Mobile Number
                </label>
                <div className="mt-1">
                  <input
                    id="mobile"
                    name="mobile"
                    type="tel"
                    required
                    value={formData.mobile_number}
                    onChange={(e) => setFormData({ ...formData, mobile_number: e.target.value })}
                    placeholder="+91 98765 43210"
                    className="input"
                  />
                </div>
              </div>

              {!otpSent ? (
                <LoadingButton
                  type="button"
                  onClick={handleSendOTP}
                  isLoading={isLoading}
                  className="w-full btn-primary"
                >
                  Send OTP
                </LoadingButton>
              ) : (
                <>
                  <div>
                    <label htmlFor="otp" className="block text-sm font-medium text-gray-700">
                      Enter OTP
                    </label>
                    <div className="mt-1">
                      <input
                        id="otp"
                        name="otp"
                        type="text"
                        required
                        value={formData.otp}
                        onChange={(e) => setFormData({ ...formData, otp: e.target.value })}
                        placeholder="123456"
                        className="input"
                        maxLength={6}
                      />
                    </div>
                  </div>

                  <div className="flex space-x-3">
                    <LoadingButton
                      type="submit"
                      isLoading={isLoading}
                      className="flex-1 btn-primary"
                    >
                      Verify & Login
                    </LoadingButton>
                    
                    {otpTimer === 0 ? (
                      <LoadingButton
                        type="button"
                        onClick={handleResendOTP}
                        isLoading={isLoading}
                        className="btn-outline"
                      >
                        Resend
                      </LoadingButton>
                    ) : (
                      <button
                        type="button"
                        disabled
                        className="btn-outline opacity-50 cursor-not-allowed"
                      >
                        {otpTimer}s
                      </button>
                    )}
                  </div>
                </>
              )}
            </form>
        </div>
      </div>
    </div>
  );
}
