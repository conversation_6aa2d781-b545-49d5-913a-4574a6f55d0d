# Debugging Guide for Authentication and Cart Issues

## Issues Identified

Based on the analysis of your Next.js customer app, here are the main issues and their fixes:

### 1. Double OTP Issue During Registration

**Problem**: The registration flow calls both `registerMobile()` and `sendOTP()` sequentially, potentially causing two OTPs to be sent.

**Root Cause**: 
- Django backend might automatically send OTP during registration
- Frontend is making an additional `sendOTP()` call

**Fix Applied**:
- Modified registration flow to handle OTP sending more gracefully
- Added error handling for redundant OTP requests
- Added console logging to track the flow

### 2. Login After Registration Failure

**Problem**: After OTP verification, the login call fails because the OTP might be consumed/invalidated.

**Root Cause**:
- OTP is single-use and gets consumed during verification
- Attempting to use the same OTP for login fails

**Fix Applied**:
- Improved error handling in verification flow
- Added fallback mechanism to send new OTP for login if needed
- Better user feedback for different scenarios

### 3. Cart Authentication Issues

**Problem**: Cart operations fail due to authentication problems.

**Root Cause**:
- JWT tokens not properly managed
- Cart API calls made without proper authentication

**Fix Applied**:
- Enhanced cart context with better error handling
- Added authentication status logging
- Improved error messages for different scenarios

## Debugging Tools Added

### 1. Environment Check Page (`/env-check`)
- Checks Django API connectivity
- Verifies proxy configuration
- Shows environment variables
- Provides troubleshooting guide

### 2. API Debug Console (`/debug-api`)
- Test individual API endpoints
- Complete registration and login flows
- Real-time results display
- User authentication status

### 3. Enhanced Logging
- Added console logging throughout the authentication flow
- API request/response logging
- Error details logging

## Step-by-Step Debugging Process

### Step 1: Verify Django Backend is Running

1. Make sure Django server is running:
   ```bash
   cd C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend
   python manage.py runserver 8000
   ```

2. Test Django API directly:
   ```bash
   curl http://localhost:8000/api/catalogue/categories/
   ```

### Step 2: Check API Connectivity

1. Open your Next.js app: `http://localhost:3000`
2. Navigate to: `http://localhost:3000/env-check`
3. Verify both direct API and proxy connections work

### Step 3: Test Authentication Flow

1. Navigate to: `http://localhost:3000/debug-api`
2. Update test data (mobile number, name, OTP)
3. Test individual API calls:
   - Register Mobile
   - Send OTP
   - Verify OTP
   - Login Mobile

4. Test complete flows:
   - Complete Registration Flow
   - Complete Login Flow

### Step 4: Check Browser Console

1. Open browser developer tools (F12)
2. Go to Console tab
3. Look for detailed API request/response logs
4. Check for any error messages

### Step 5: Test Real User Flow

1. Go to registration page: `http://localhost:3000/auth/register`
2. Enter mobile number and name
3. Click "Create Account"
4. Check console for logs
5. Enter OTP and verify

## Common Issues and Solutions

### Issue: "Mobile number not registered" during login

**Solution**: 
- User needs to register first
- Check if registration was successful
- Verify user exists in Django database

### Issue: "Invalid OTP" errors

**Possible Causes**:
- OTP expired (usually 5-10 minutes)
- OTP already used
- Wrong OTP entered
- Django OTP generation issues

**Solutions**:
- Use resend OTP functionality
- Check Django OTP settings
- Verify OTP generation in Django logs

### Issue: Cart operations fail

**Possible Causes**:
- User not authenticated
- JWT token expired
- Django cart API issues

**Solutions**:
- Ensure user is logged in
- Check token refresh mechanism
- Verify Django cart endpoints

### Issue: Two OTPs received

**Possible Causes**:
- Django registration automatically sends OTP
- Frontend makes additional OTP request

**Solutions**:
- Check Django registration endpoint behavior
- Modify frontend to handle automatic OTP sending

## Django Backend Checklist

Ensure your Django backend has:

1. **CORS Configuration**:
   ```python
   CORS_ALLOWED_ORIGINS = [
       "http://localhost:3000",
   ]
   ```

2. **JWT Settings**:
   ```python
   SIMPLE_JWT = {
       'ACCESS_TOKEN_LIFETIME': timedelta(minutes=60),
       'REFRESH_TOKEN_LIFETIME': timedelta(days=7),
       # ... other settings
   }
   ```

3. **OTP Settings**:
   - OTP expiry time
   - OTP length (6 digits)
   - Single-use OTP validation

4. **Authentication Endpoints**:
   - `/api/auth/register/mobile/`
   - `/api/auth/otp/send/`
   - `/api/auth/otp/verify/`
   - `/api/auth/login/mobile/`

## Next Steps

1. **Run the debugging tools** to identify specific issues
2. **Check Django logs** for backend errors
3. **Monitor browser console** for frontend errors
4. **Test with different mobile numbers** to isolate issues
5. **Verify Django database** for user registration status

## Files Modified

- `src/app/auth/register/page.tsx` - Improved registration flow
- `src/app/auth/login/page.tsx` - Enhanced login handling
- `src/contexts/CartContext.tsx` - Better cart error handling
- `src/lib/api.ts` - Added comprehensive logging
- `src/app/debug-api/page.tsx` - New debugging tool
- `src/app/env-check/page.tsx` - New connectivity checker

## Contact Points

If issues persist:
1. Check Django server logs for backend errors
2. Use the debug tools to isolate the problem
3. Verify Django API responses match expected format
4. Test with a fresh user registration
