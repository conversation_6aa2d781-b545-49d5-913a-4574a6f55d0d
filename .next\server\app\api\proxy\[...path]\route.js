"use strict";(()=>{var e={};e.id=626,e.ids=[626],e.modules={517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3488:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>P,originalPathname:()=>O,patchFetch:()=>w,requestAsyncStorage:()=>m,routeModule:()=>f,serverHooks:()=>y,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>b});var o={};r.r(o),r.d(o,{DELETE:()=>d,GET:()=>u,OPTIONS:()=>p,POST:()=>h,PUT:()=>c});var n=r(5419),s=r(9108),a=r(9678),i=r(8070);let l="http://localhost:8000/api";async function u(e,{params:t}){try{let{searchParams:r,pathname:o}=new URL(e.url),n=r.toString(),s=t.path.join("/"),a=s;a.length>0&&!a.endsWith("/")&&(a+="/");let u=`${l}/${a}${n?`?${n}`:""}`;console.log("Proxying GET request to:",u);let h={"Content-Type":"application/json"},c=e.headers.get("authorization");c&&(h.Authorization=c);let d=await fetch(u,{method:"GET",headers:h});if(!d.ok){let e;let t=await d.text();console.error("Backend error:",d.status,t);try{e=JSON.parse(t)}catch{e={error:t||d.statusText}}return i.Z.json(e,{status:d.status,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, POST, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}})}let p=await d.json();return console.log("Proxy response received for:",s),i.Z.json(p,{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, POST, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}})}catch(e){return console.error("Proxy error:",e),i.Z.json({error:e.message||"Internal server error"},{status:500})}}async function h(e,{params:t}){try{let{searchParams:r,pathname:o}=new URL(e.url),n=r.toString(),s=t.path.join("/");s.length>0&&!s.endsWith("/")&&(s+="/");let a=`${l}/${s}${n?`?${n}`:""}`,u=await e.text();console.log("Proxying POST request to:",a);let h={"Content-Type":"application/json"},c=e.headers.get("authorization");c&&(h.Authorization=c);let d=await fetch(a,{method:"POST",headers:h,body:u});if(!d.ok){let e;let t=await d.text();console.error("Backend error:",d.status,t);try{e=JSON.parse(t)}catch{e={error:t||d.statusText}}return i.Z.json(e,{status:d.status,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, POST, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}})}let p=await d.json();return i.Z.json(p,{status:d.status,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, POST, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}})}catch(e){return console.error("Proxy error:",e),i.Z.json({error:e.message||"Internal server error"},{status:500})}}async function c(e,{params:t}){try{let{searchParams:r,pathname:o}=new URL(e.url),n=r.toString(),s=t.path.join("/");s.length>0&&!s.endsWith("/")&&(s+="/");let a=`${l}/${s}${n?`?${n}`:""}`,u=await e.text();console.log("Proxying PUT request to:",a);let h={"Content-Type":"application/json"},c=e.headers.get("authorization");c&&(h.Authorization=c);let d=await fetch(a,{method:"PUT",headers:h,body:u});if(!d.ok){let e;let t=await d.text();console.error("Backend error:",d.status,t);try{e=JSON.parse(t)}catch{e={error:t||d.statusText}}return i.Z.json(e,{status:d.status,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, POST, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}})}let p=await d.json();return i.Z.json(p,{status:d.status,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, POST, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}})}catch(e){return console.error("Proxy error:",e),i.Z.json({error:e.message||"Internal server error"},{status:500})}}async function d(e,{params:t}){try{let{searchParams:r,pathname:o}=new URL(e.url),n=r.toString(),s=t.path.join("/");s.length>0&&!s.endsWith("/")&&(s+="/");let a=`${l}/${s}${n?`?${n}`:""}`;console.log("Proxying DELETE request to:",a);let u={},h=e.headers.get("authorization");h&&(u.Authorization=h);let c=await fetch(a,{method:"DELETE",headers:u});if(!c.ok){let e;let t=await c.text();console.error("Backend error:",c.status,t);try{e=JSON.parse(t)}catch{e={error:t||c.statusText}}return i.Z.json(e,{status:c.status,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, POST, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}})}if(204===c.status)return new i.Z(null,{status:204,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, POST, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}});let d=await c.json();return i.Z.json(d,{status:c.status,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, POST, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}})}catch(e){return console.error("Proxy error:",e),i.Z.json({error:e.message||"Internal server error"},{status:500})}}async function p(){return new i.Z(null,{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, POST, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}})}let f=new n.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/proxy/[...path]/route",pathname:"/api/proxy/[...path]",filename:"route",bundlePath:"app/api/proxy/[...path]/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\api\\proxy\\[...path]\\route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:m,staticGenerationAsyncStorage:g,serverHooks:y,headerHooks:P,staticGenerationBailout:b}=f,O="/api/proxy/[...path]/route";function w(){return(0,a.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:g})}},7347:e=>{var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,n=Object.prototype.hasOwnProperty,s={};function a(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean);return`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}; ${r.join("; ")}`}function i(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[o,n]=[r.slice(0,e),r.slice(e+1)];try{t.set(o,decodeURIComponent(null!=n?n:"true"))}catch{}}return t}function l(e){var t,r;if(!e)return;let[[o,n],...s]=i(e),{domain:a,expires:l,httponly:c,maxage:d,path:p,samesite:f,secure:m,priority:g}=Object.fromEntries(s.map(([e,t])=>[e.toLowerCase(),t]));return function(e){let t={};for(let r in e)e[r]&&(t[r]=e[r]);return t}({name:o,value:decodeURIComponent(n),domain:a,...l&&{expires:new Date(l)},...c&&{httpOnly:!0},..."string"==typeof d&&{maxAge:Number(d)},path:p,...f&&{sameSite:u.includes(t=(t=f).toLowerCase())?t:void 0},...m&&{secure:!0},...g&&{priority:h.includes(r=(r=g).toLowerCase())?r:void 0}})}((e,r)=>{for(var o in r)t(e,o,{get:r[o],enumerable:!0})})(s,{RequestCookies:()=>c,ResponseCookies:()=>d,parseCookie:()=>i,parseSetCookie:()=>l,stringifyCookie:()=>a}),e.exports=((e,s,a,i)=>{if(s&&"object"==typeof s||"function"==typeof s)for(let a of o(s))n.call(e,a)||void 0===a||t(e,a,{get:()=>s[a],enumerable:!(i=r(s,a))||i.enumerable});return e})(t({},"__esModule",{value:!0}),s);var u=["strict","lax","none"],h=["low","medium","high"],c=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of i(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let o="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===o).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,o=this._parsed;return o.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(o).map(([e,t])=>a(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>a(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},d=class{constructor(e){var t,r,o;this._parsed=new Map,this._headers=e;let n=null!=(o=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?o:[];for(let e of Array.isArray(n)?n:function(e){if(!e)return[];var t,r,o,n,s,a=[],i=0;function l(){for(;i<e.length&&/\s/.test(e.charAt(i));)i+=1;return i<e.length}for(;i<e.length;){for(t=i,s=!1;l();)if(","===(r=e.charAt(i))){for(o=i,i+=1,l(),n=i;i<e.length&&"="!==(r=e.charAt(i))&&";"!==r&&","!==r;)i+=1;i<e.length&&"="===e.charAt(i)?(s=!0,i=n,a.push(e.substring(t,o)),t=i):i=o+1}else i+=1;(!s||i>=e.length)&&a.push(e.substring(t,e.length))}return a}(n)){let t=l(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let o="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===o)}has(e){return this._parsed.has(e)}set(...e){let[t,r,o]=1===e.length?[e[0].name,e[0].value,e[0]]:e,n=this._parsed;return n.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...o})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=a(r);t.append("set-cookie",e)}}(n,this._headers),this}delete(...e){let[t,r,o]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0].path,e[0].domain];return this.set({name:t,path:r,domain:o,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(a).join("; ")}}},5419:(e,t,r)=>{e.exports=r(517)},8070:(e,t,r)=>{Object.defineProperty(t,"Z",{enumerable:!0,get:function(){return o.NextResponse}});let o=r(457)},514:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"NextURL",{enumerable:!0,get:function(){return h}});let o=r(737),n=r(5418),s=r(283),a=r(3588),i=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function l(e,t){return new URL(String(e).replace(i,"localhost"),t&&String(t).replace(i,"localhost"))}let u=Symbol("NextURLInternal");class h{constructor(e,t,r){let o,n;"object"==typeof t&&"pathname"in t||"string"==typeof t?(o=t,n=r||{}):n=r||t||{},this[u]={url:l(e,o??n.base),options:n,basePath:""},this.analyze()}analyze(){var e,t,r,n,i;let l=(0,a.getNextPathnameInfo)(this[u].url.pathname,{nextConfig:this[u].options.nextConfig,parseData:!0,i18nProvider:this[u].options.i18nProvider}),h=(0,s.getHostname)(this[u].url,this[u].options.headers);this[u].domainLocale=this[u].options.i18nProvider?this[u].options.i18nProvider.detectDomainLocale(h):(0,o.detectDomainLocale)(null==(t=this[u].options.nextConfig)?void 0:null==(e=t.i18n)?void 0:e.domains,h);let c=(null==(r=this[u].domainLocale)?void 0:r.defaultLocale)||(null==(i=this[u].options.nextConfig)?void 0:null==(n=i.i18n)?void 0:n.defaultLocale);this[u].url.pathname=l.pathname,this[u].defaultLocale=c,this[u].basePath=l.basePath??"",this[u].buildId=l.buildId,this[u].locale=l.locale??c,this[u].trailingSlash=l.trailingSlash}formatPathname(){return(0,n.formatNextPathnameInfo)({basePath:this[u].basePath,buildId:this[u].buildId,defaultLocale:this[u].options.forceLocale?void 0:this[u].defaultLocale,locale:this[u].locale,pathname:this[u].url.pathname,trailingSlash:this[u].trailingSlash})}formatSearch(){return this[u].url.search}get buildId(){return this[u].buildId}set buildId(e){this[u].buildId=e}get locale(){return this[u].locale??""}set locale(e){var t,r;if(!this[u].locale||!(null==(r=this[u].options.nextConfig)?void 0:null==(t=r.i18n)?void 0:t.locales.includes(e)))throw TypeError(`The NextURL configuration includes no locale "${e}"`);this[u].locale=e}get defaultLocale(){return this[u].defaultLocale}get domainLocale(){return this[u].domainLocale}get searchParams(){return this[u].url.searchParams}get host(){return this[u].url.host}set host(e){this[u].url.host=e}get hostname(){return this[u].url.hostname}set hostname(e){this[u].url.hostname=e}get port(){return this[u].url.port}set port(e){this[u].url.port=e}get protocol(){return this[u].url.protocol}set protocol(e){this[u].url.protocol=e}get href(){let e=this.formatPathname(),t=this.formatSearch();return`${this.protocol}//${this.host}${e}${t}${this.hash}`}set href(e){this[u].url=l(e),this.analyze()}get origin(){return this[u].url.origin}get pathname(){return this[u].url.pathname}set pathname(e){this[u].url.pathname=e}get hash(){return this[u].url.hash}set hash(e){this[u].url.hash=e}get search(){return this[u].url.search}set search(e){this[u].url.search=e}get password(){return this[u].url.password}set password(e){this[u].url.password=e}get username(){return this[u].url.username}set username(e){this[u].url.username=e}get basePath(){return this[u].basePath}set basePath(e){this[u].basePath=e.startsWith("/")?e:`/${e}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new h(String(this),this[u].options)}}},3608:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RequestCookies:function(){return o.RequestCookies},ResponseCookies:function(){return o.ResponseCookies}});let o=r(7347)},457:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"NextResponse",{enumerable:!0,get:function(){return u}});let o=r(514),n=r(8670),s=r(3608),a=Symbol("internal response"),i=new Set([301,302,303,307,308]);function l(e,t){var r;if(null==e?void 0:null==(r=e.request)?void 0:r.headers){if(!(e.request.headers instanceof Headers))throw Error("request.headers must be an instance of Headers");let r=[];for(let[o,n]of e.request.headers)t.set("x-middleware-request-"+o,n),r.push(o);t.set("x-middleware-override-headers",r.join(","))}}class u extends Response{constructor(e,t={}){super(e,t),this[a]={cookies:new s.ResponseCookies(this.headers),url:t.url?new o.NextURL(t.url,{headers:(0,n.toNodeOutgoingHttpHeaders)(this.headers),nextConfig:t.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[a].cookies}static json(e,t){let r=Response.json(e,t);return new u(r.body,r)}static redirect(e,t){let r="number"==typeof t?t:(null==t?void 0:t.status)??307;if(!i.has(r))throw RangeError('Failed to execute "redirect" on "response": Invalid status code');let o="object"==typeof t?t:{},s=new Headers(null==o?void 0:o.headers);return s.set("Location",(0,n.validateURL)(e)),new u(null,{...o,headers:s,status:r})}static rewrite(e,t){let r=new Headers(null==t?void 0:t.headers);return r.set("x-middleware-rewrite",(0,n.validateURL)(e)),l(t,r),new u(null,{...t,headers:r})}static next(e){let t=new Headers(null==e?void 0:e.headers);return t.set("x-middleware-next","1"),l(e,t),new u(null,{...e,headers:t})}}},8670:(e,t)=>{function r(e){let t=new Headers;for(let[r,o]of Object.entries(e))for(let e of Array.isArray(o)?o:[o])void 0!==e&&("number"==typeof e&&(e=e.toString()),t.append(r,e));return t}function o(e){var t,r,o,n,s,a=[],i=0;function l(){for(;i<e.length&&/\s/.test(e.charAt(i));)i+=1;return i<e.length}for(;i<e.length;){for(t=i,s=!1;l();)if(","===(r=e.charAt(i))){for(o=i,i+=1,l(),n=i;i<e.length&&"="!==(r=e.charAt(i))&&";"!==r&&","!==r;)i+=1;i<e.length&&"="===e.charAt(i)?(s=!0,i=n,a.push(e.substring(t,o)),t=i):i=o+1}else i+=1;(!s||i>=e.length)&&a.push(e.substring(t,e.length))}return a}function n(e){let t={},r=[];if(e)for(let[n,s]of e.entries())"set-cookie"===n.toLowerCase()?(r.push(...o(s)),t[n]=1===r.length?r[0]:r):t[n]=s;return t}function s(e){try{return String(new URL(String(e)))}catch(t){throw Error(`URL is malformed "${String(e)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:t})}}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fromNodeOutgoingHttpHeaders:function(){return r},splitCookiesString:function(){return o},toNodeOutgoingHttpHeaders:function(){return n},validateURL:function(){return s}})},283:(e,t)=>{function r(e,t){let r;if((null==t?void 0:t.host)&&!Array.isArray(t.host))r=t.host.toString().split(":",1)[0];else{if(!e.hostname)return;r=e.hostname}return r.toLowerCase()}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getHostname",{enumerable:!0,get:function(){return r}})},737:(e,t)=>{function r(e,t,r){if(e)for(let s of(r&&(r=r.toLowerCase()),e)){var o,n;if(t===(null==(o=s.domain)?void 0:o.split(":",1)[0].toLowerCase())||r===s.defaultLocale.toLowerCase()||(null==(n=s.locales)?void 0:n.some(e=>e.toLowerCase()===r)))return s}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"detectDomainLocale",{enumerable:!0,get:function(){return r}})},3935:(e,t)=>{function r(e,t){let r;let o=e.split("/");return(t||[]).some(t=>!!o[1]&&o[1].toLowerCase()===t.toLowerCase()&&(r=t,o.splice(1,1),e=o.join("/")||"/",!0)),{pathname:e,detectedLocale:r}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizeLocalePath",{enumerable:!0,get:function(){return r}})},8030:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addLocale",{enumerable:!0,get:function(){return s}});let o=r(3495),n=r(7211);function s(e,t,r,s){if(!t||t===r)return e;let a=e.toLowerCase();return!s&&((0,n.pathHasPrefix)(a,"/api")||(0,n.pathHasPrefix)(a,"/"+t.toLowerCase()))?e:(0,o.addPathPrefix)(e,"/"+t)}},3495:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return n}});let o=r(1955);function n(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:s}=(0,o.parsePath)(e);return""+t+r+n+s}},2348:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathSuffix",{enumerable:!0,get:function(){return n}});let o=r(1955);function n(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:s}=(0,o.parsePath)(e);return""+r+t+n+s}},5418:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"formatNextPathnameInfo",{enumerable:!0,get:function(){return i}});let o=r(5545),n=r(3495),s=r(2348),a=r(8030);function i(e){let t=(0,a.addLocale)(e.pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix);return(e.buildId||!e.trailingSlash)&&(t=(0,o.removeTrailingSlash)(t)),e.buildId&&(t=(0,s.addPathSuffix)((0,n.addPathPrefix)(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=(0,n.addPathPrefix)(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:(0,s.addPathSuffix)(t,"/"):(0,o.removeTrailingSlash)(t)}},3588:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getNextPathnameInfo",{enumerable:!0,get:function(){return a}});let o=r(3935),n=r(7188),s=r(7211);function a(e,t){var r,a;let{basePath:i,i18n:l,trailingSlash:u}=null!=(r=t.nextConfig)?r:{},h={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):u};i&&(0,s.pathHasPrefix)(h.pathname,i)&&(h.pathname=(0,n.removePathPrefix)(h.pathname,i),h.basePath=i);let c=h.pathname;if(h.pathname.startsWith("/_next/data/")&&h.pathname.endsWith(".json")){let e=h.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/"),r=e[0];h.buildId=r,c="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(h.pathname=c)}if(l){let e=t.i18nProvider?t.i18nProvider.analyze(h.pathname):(0,o.normalizeLocalePath)(h.pathname,l.locales);h.locale=e.detectedLocale,h.pathname=null!=(a=e.pathname)?a:h.pathname,!e.detectedLocale&&h.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(c):(0,o.normalizeLocalePath)(c,l.locales)).detectedLocale&&(h.locale=e.detectedLocale)}return h}},1955:(e,t)=>{function r(e){let t=e.indexOf("#"),r=e.indexOf("?"),o=r>-1&&(t<0||r<t);return o||t>-1?{pathname:e.substring(0,o?r:t),query:o?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return r}})},7211:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return n}});let o=r(1955);function n(e,t){if("string"!=typeof e)return!1;let{pathname:r}=(0,o.parsePath)(e);return r===t||r.startsWith(t+"/")}},7188:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removePathPrefix",{enumerable:!0,get:function(){return n}});let o=r(7211);function n(e,t){if(!(0,o.pathHasPrefix)(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}},5545:(e,t)=>{function r(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return r}})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[638],()=>r(3488));module.exports=o})();