if(!self.define){let e,s={};const t=(t,a)=>(t=new URL(t+".js",a).href,s[t]||new Promise((s=>{if("document"in self){const e=document.createElement("script");e.src=t,e.onload=s,document.head.appendChild(e)}else e=t,importScripts(t),s()})).then((()=>{let e=s[t];if(!e)throw new Error(`Module ${t} didn’t register its module`);return e})));self.define=(a,n)=>{const i=e||("document"in self?document.currentScript.src:"")||location.href;if(s[i])return;let c={};const u=e=>t(e,i),o={module:{uri:i},exports:c,require:u};s[i]=Promise.all(a.map((e=>o[e]||u(e)))).then((e=>(n(...e),c)))}}define(["./workbox-4754cb34"],(function(e){"use strict";importScripts(),self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"/_next/app-build-manifest.json",revision:"43fe324582dc2889fe9157dd1337a654"},{url:"/_next/static/-ytYhuoK_vZyPNs8VvuCj/_buildManifest.js",revision:"41a968fdf4a5a66f7c385d388f55a779"},{url:"/_next/static/-ytYhuoK_vZyPNs8VvuCj/_ssgManifest.js",revision:"b6652df95db52feb4daf4eca35380933"},{url:"/_next/static/chunks/159-c9690f571172717e.js",revision:"-ytYhuoK_vZyPNs8VvuCj"},{url:"/_next/static/chunks/221-7cc18aa9a8a91fdc.js",revision:"-ytYhuoK_vZyPNs8VvuCj"},{url:"/_next/static/chunks/224-31279b99a6e91423.js",revision:"-ytYhuoK_vZyPNs8VvuCj"},{url:"/_next/static/chunks/250-7ce959dedb29e7ed.js",revision:"-ytYhuoK_vZyPNs8VvuCj"},{url:"/_next/static/chunks/652-67232859384e2bd6.js",revision:"-ytYhuoK_vZyPNs8VvuCj"},{url:"/_next/static/chunks/938-ee36b187d0dcebb1.js",revision:"-ytYhuoK_vZyPNs8VvuCj"},{url:"/_next/static/chunks/995-a688179e082778df.js",revision:"-ytYhuoK_vZyPNs8VvuCj"},{url:"/_next/static/chunks/app/addresses/page-1db38d6d7a817f57.js",revision:"-ytYhuoK_vZyPNs8VvuCj"},{url:"/_next/static/chunks/app/auth/login/page-ed1f091423e17072.js",revision:"-ytYhuoK_vZyPNs8VvuCj"},{url:"/_next/static/chunks/app/auth/register/page-5bd7a68bd1029f71.js",revision:"-ytYhuoK_vZyPNs8VvuCj"},{url:"/_next/static/chunks/app/cart/page-3067e535c4abe5ed.js",revision:"-ytYhuoK_vZyPNs8VvuCj"},{url:"/_next/static/chunks/app/categories/%5Bslug%5D/page-5d7f3a05f094c094.js",revision:"-ytYhuoK_vZyPNs8VvuCj"},{url:"/_next/static/chunks/app/checkout/address/page-c7f92eebd1e699f7.js",revision:"-ytYhuoK_vZyPNs8VvuCj"},{url:"/_next/static/chunks/app/checkout/payment/page-9a99b7fcf8477a0a.js",revision:"-ytYhuoK_vZyPNs8VvuCj"},{url:"/_next/static/chunks/app/checkout/schedule/page-1b1689f4259fae0c.js",revision:"-ytYhuoK_vZyPNs8VvuCj"},{url:"/_next/static/chunks/app/contact/page-ca8a7807ff4a8d1e.js",revision:"-ytYhuoK_vZyPNs8VvuCj"},{url:"/_next/static/chunks/app/debug-api/page-43041f5e2f068aeb.js",revision:"-ytYhuoK_vZyPNs8VvuCj"},{url:"/_next/static/chunks/app/env-check/page-d147f427628f906d.js",revision:"-ytYhuoK_vZyPNs8VvuCj"},{url:"/_next/static/chunks/app/error-a729b935379cac07.js",revision:"-ytYhuoK_vZyPNs8VvuCj"},{url:"/_next/static/chunks/app/layout-d4dcdaa886f9f524.js",revision:"-ytYhuoK_vZyPNs8VvuCj"},{url:"/_next/static/chunks/app/loading-37f974aef2a5829b.js",revision:"-ytYhuoK_vZyPNs8VvuCj"},{url:"/_next/static/chunks/app/not-found-a9caf70dab008aee.js",revision:"-ytYhuoK_vZyPNs8VvuCj"},{url:"/_next/static/chunks/app/orders/%5BorderNumber%5D/page-d399d31a41f59b24.js",revision:"-ytYhuoK_vZyPNs8VvuCj"},{url:"/_next/static/chunks/app/orders/page-1243d8d671781d5c.js",revision:"-ytYhuoK_vZyPNs8VvuCj"},{url:"/_next/static/chunks/app/page-51be9f38858ab743.js",revision:"-ytYhuoK_vZyPNs8VvuCj"},{url:"/_next/static/chunks/app/profile/page-1c85a3a36882e4d4.js",revision:"-ytYhuoK_vZyPNs8VvuCj"},{url:"/_next/static/chunks/app/search/page-0b2083e59260d4ea.js",revision:"-ytYhuoK_vZyPNs8VvuCj"},{url:"/_next/static/chunks/app/services/%5Bslug%5D/page-f1c055ef07d10e6c.js",revision:"-ytYhuoK_vZyPNs8VvuCj"},{url:"/_next/static/chunks/app/services/page-4df7206d042986fb.js",revision:"-ytYhuoK_vZyPNs8VvuCj"},{url:"/_next/static/chunks/app/test-auth/page-4d256553d3c5ec40.js",revision:"-ytYhuoK_vZyPNs8VvuCj"},{url:"/_next/static/chunks/fd9d1056-bee54734699614b4.js",revision:"-ytYhuoK_vZyPNs8VvuCj"},{url:"/_next/static/chunks/framework-c5181c9431ddc45b.js",revision:"-ytYhuoK_vZyPNs8VvuCj"},{url:"/_next/static/chunks/main-a441a16e0c58cb8b.js",revision:"-ytYhuoK_vZyPNs8VvuCj"},{url:"/_next/static/chunks/main-app-f93f729d78eedab7.js",revision:"-ytYhuoK_vZyPNs8VvuCj"},{url:"/_next/static/chunks/pages/_app-98cb51ec6f9f135f.js",revision:"-ytYhuoK_vZyPNs8VvuCj"},{url:"/_next/static/chunks/pages/_error-9157e57c362a0d0d.js",revision:"-ytYhuoK_vZyPNs8VvuCj"},{url:"/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js",revision:"837c0df77fd5009c9e46d446188ecfd0"},{url:"/_next/static/chunks/webpack-8a7503ac3bafb50e.js",revision:"-ytYhuoK_vZyPNs8VvuCj"},{url:"/_next/static/css/81e27d4010c79801.css",revision:"81e27d4010c79801"},{url:"/_next/static/media/26a46d62cd723877-s.woff2",revision:"befd9c0fdfa3d8a645d5f95717ed6420"},{url:"/_next/static/media/55c55f0601d81cf3-s.woff2",revision:"43828e14271c77b87e3ed582dbff9f74"},{url:"/_next/static/media/581909926a08bbc8-s.woff2",revision:"f0b86e7c24f455280b8df606b89af891"},{url:"/_next/static/media/8e9860b6e62d6359-s.woff2",revision:"01ba6c2a184b8cba08b0d57167664d75"},{url:"/_next/static/media/97e0cb1ae144a2a9-s.woff2",revision:"e360c61c5bd8d90639fd4503c829c2dc"},{url:"/_next/static/media/df0a9ae256c0569c-s.woff2",revision:"d54db44de5ccb18886ece2fda72bdfe0"},{url:"/_next/static/media/e4af272ccee01ff0-s.p.woff2",revision:"65850a373e258f1c897a2b3d75eb74de"},{url:"/favicon.ico",revision:"e4d13187ccd69d9840e28588621cfa79"},{url:"/icons/README.md",revision:"c3f5f9239b745d0aa5711455ba5ad4f3"},{url:"/manifest.json",revision:"36aa0439ce6369286ee9692504c8c24b"}],{ignoreURLParametersMatching:[]}),e.cleanupOutdatedCaches(),e.registerRoute("/",new e.NetworkFirst({cacheName:"start-url",plugins:[{cacheWillUpdate:async({request:e,response:s,event:t,state:a})=>s&&"opaqueredirect"===s.type?new Response(s.body,{status:200,statusText:"OK",headers:s.headers}):s}]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:gstatic)\.com\/.*/i,new e.CacheFirst({cacheName:"google-fonts-webfonts",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:31536e3})]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:googleapis)\.com\/.*/i,new e.StaleWhileRevalidate({cacheName:"google-fonts-stylesheets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:eot|otf|ttc|ttf|woff|woff2|font.css)$/i,new e.StaleWhileRevalidate({cacheName:"static-font-assets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:jpg|jpeg|gif|png|svg|ico|webp)$/i,new e.StaleWhileRevalidate({cacheName:"static-image-assets",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/image\?url=.+$/i,new e.StaleWhileRevalidate({cacheName:"next-image",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp3|wav|ogg)$/i,new e.CacheFirst({cacheName:"static-audio-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp4)$/i,new e.CacheFirst({cacheName:"static-video-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:js)$/i,new e.StaleWhileRevalidate({cacheName:"static-js-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:css|less)$/i,new e.StaleWhileRevalidate({cacheName:"static-style-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/data\/.+\/.+\.json$/i,new e.StaleWhileRevalidate({cacheName:"next-data",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:json|xml|csv)$/i,new e.NetworkFirst({cacheName:"static-data-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute((({url:e})=>{if(!(self.origin===e.origin))return!1;const s=e.pathname;return!s.startsWith("/api/auth/")&&!!s.startsWith("/api/")}),new e.NetworkFirst({cacheName:"apis",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:16,maxAgeSeconds:86400})]}),"GET"),e.registerRoute((({url:e})=>{if(!(self.origin===e.origin))return!1;return!e.pathname.startsWith("/api/")}),new e.NetworkFirst({cacheName:"others",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute((({url:e})=>!(self.origin===e.origin)),new e.NetworkFirst({cacheName:"cross-origin",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:3600})]}),"GET")}));
