(()=>{var e={};e.id=178,e.ids=[178],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},1877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},5319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},7441:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>o});var r=t(482),a=t(9108),l=t(2563),i=t.n(l),n=t(8300),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let o=["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,5648)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\profile\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,1008)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,4117)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,2793)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,8206)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\not-found.tsx"]}],d=["C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\profile\\page.tsx"],m="/profile/page",x={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},7674:(e,s,t)=>{Promise.resolve().then(t.bind(t,8456))},3592:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(9224).Z)("Facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]])},3632:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(9224).Z)("Instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]])},8120:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(9224).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},1206:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(9224).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},508:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(9224).Z)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},8200:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(9224).Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},1917:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(9224).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},626:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(9224).Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},2401:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(9224).Z)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},5576:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(9224).Z)("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]])},8822:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(9224).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},2254:(e,s,t)=>{e.exports=t(4767)},8456:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>g});var r=t(2295),a=t(3729),l=t(9224);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,l.Z)("Pen",[["path",{d:"M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z",key:"5qss01"}]]);var n=t(8822),c=t(626),o=t(1206);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let d=(0,l.Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]]);var m=t(4513),x=t(8629),u=t(4418),p=t(828),h=t(5814),y=t(1448),b=t(6548);function g(){let{user:e,updateUser:s}=(0,h.useAuth)(),{showToast:t}=(0,y.useToast)(),[l,g]=(0,a.useState)(!1),[j,v]=(0,a.useState)(!1),[f,N]=(0,a.useState)({name:e?.name||"",mobile_number:e?.mobile_number||""}),[_,k]=(0,a.useState)(!1),[P,w]=(0,a.useState)(""),[Z,M]=(0,a.useState)(0),C=()=>{M(60);let e=setInterval(()=>{M(s=>s<=1?(clearInterval(e),0):s-1)},1e3)},O=()=>{g(!1),k(!1),w(""),N({name:e?.name||"",mobile_number:e?.mobile_number||""})},A=async()=>{if(!f.name.trim()){t({type:"error",title:"Name is required"});return}if(f.mobile_number!==e?.mobile_number)try{await b.iJ.sendOTP(f.mobile_number),k(!0),C(),t({type:"success",title:"OTP sent to new mobile number"})}catch(e){t({type:"error",title:"Failed to send OTP",message:e.message})}else await q()},q=async()=>{v(!0);try{let r={name:f.name};f.mobile_number!==e?.mobile_number&&P&&(await b.iJ.verifyOTP(f.mobile_number,P),r.mobile_number=f.mobile_number),await b.iJ.updateProfile(r),s(r),g(!1),k(!1),w(""),t({type:"success",title:"Profile updated successfully"})}catch(e){t({type:"error",title:"Failed to update profile",message:e.message})}finally{v(!1)}},D=async()=>{if(!P.trim()){t({type:"error",title:"Please enter OTP"});return}await q()},S=async()=>{try{await b.iJ.sendOTP(f.mobile_number),C(),t({type:"success",title:"OTP resent successfully"})}catch(e){t({type:"error",title:"Failed to resend OTP",message:e.message})}};return e?r.jsx(x.Z,{children:r.jsx(u.i,{children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-8",children:"My Profile"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[r.jsx("div",{className:"lg:col-span-2",children:(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[r.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Personal Information"}),!l&&(0,r.jsxs)("button",{onClick:()=>{g(!0),N({name:e?.name||"",mobile_number:e?.mobile_number||""})},className:"btn-outline flex items-center",children:[r.jsx(i,{className:"h-4 w-4 mr-2"}),"Edit"]})]}),_?(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[r.jsx("h3",{className:"text-lg font-medium text-blue-900 mb-2",children:"Verify New Mobile Number"}),(0,r.jsxs)("p",{className:"text-blue-700",children:["We've sent an OTP to ",f.mobile_number,". Please enter it below to verify your new mobile number."]})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Enter OTP"}),r.jsx("input",{type:"text",value:P,onChange:e=>w(e.target.value),placeholder:"123456",className:"input",maxLength:6})]}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[r.jsx(p.fl,{onClick:D,isLoading:j,className:"btn-primary",children:"Verify & Update"}),0===Z?r.jsx("button",{onClick:S,className:"btn-outline",children:"Resend OTP"}):(0,r.jsxs)("button",{disabled:!0,className:"btn-outline opacity-50 cursor-not-allowed",children:["Resend in ",Z,"s"]}),r.jsx("button",{onClick:O,className:"btn-secondary",children:"Cancel"})]})]}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name"}),l?r.jsx("input",{type:"text",value:f.name,onChange:e=>N({...f,name:e.target.value}),className:"input",placeholder:"Enter your full name"}):(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx(n.Z,{className:"h-5 w-5 text-gray-400"}),r.jsx("span",{className:"text-gray-900",children:e.name})]})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Mobile Number"}),l?r.jsx("input",{type:"tel",value:f.mobile_number,onChange:e=>N({...f,mobile_number:e.target.value}),className:"input",placeholder:"Enter your mobile number"}):(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx(c.Z,{className:"h-5 w-5 text-gray-400"}),r.jsx("span",{className:"text-gray-900",children:e.mobile_number}),e.is_verified&&r.jsx("span",{className:"bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium",children:"Verified"})]})]}),e.email&&(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx(o.Z,{className:"h-5 w-5 text-gray-400"}),r.jsx("span",{className:"text-gray-900",children:e.email})]})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Account Type"}),r.jsx("span",{className:"bg-primary-100 text-primary-800 px-3 py-1 rounded-full text-sm font-medium",children:e.user_type})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Member Since"}),r.jsx("span",{className:"text-gray-900",children:new Date(e.date_joined).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})})]}),l&&(0,r.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,r.jsxs)(p.fl,{onClick:A,isLoading:j,className:"btn-primary flex items-center",children:[r.jsx(d,{className:"h-4 w-4 mr-2"}),"Save Changes"]}),(0,r.jsxs)("button",{onClick:O,className:"btn-secondary flex items-center",children:[r.jsx(m.Z,{className:"h-4 w-4 mr-2"}),"Cancel"]})]})]})]})}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"card p-6",children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Quick Actions"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("a",{href:"/orders",className:"block w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors",children:[r.jsx("div",{className:"font-medium text-gray-900",children:"My Orders"}),r.jsx("div",{className:"text-sm text-gray-600",children:"View your service bookings"})]}),(0,r.jsxs)("a",{href:"/addresses",className:"block w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors",children:[r.jsx("div",{className:"font-medium text-gray-900",children:"Manage Addresses"}),r.jsx("div",{className:"text-sm text-gray-600",children:"Add or edit delivery addresses"})]}),(0,r.jsxs)("a",{href:"/support",className:"block w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors",children:[r.jsx("div",{className:"font-medium text-gray-900",children:"Help & Support"}),r.jsx("div",{className:"text-sm text-gray-600",children:"Get help with your account"})]})]})]}),(0,r.jsxs)("div",{className:"card p-6",children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Account Security"}),(0,r.jsxs)("div",{className:"space-y-3 text-sm",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx("span",{className:"text-gray-600",children:"Mobile Verified"}),r.jsx("span",{className:`px-2 py-1 rounded text-xs font-medium ${e.is_verified?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}`,children:e.is_verified?"Yes":"No"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx("span",{className:"text-gray-600",children:"Account Status"}),r.jsx("span",{className:"bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium",children:"Active"})]})]})]})]})]})]})})}):r.jsx(x.Z,{children:r.jsx(u.i,{children:r.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16 text-center",children:r.jsx("p",{className:"text-gray-600",children:"Loading profile..."})})})})}},4418:(e,s,t)=>{"use strict";t.d(s,{i:()=>c});var r=t(2295),a=t(3729),l=t(2254),i=t(5814),n=t(828);let c=({children:e,redirectTo:s="/auth/login",requireAuth:t=!0})=>{let{isAuthenticated:c,isLoading:o}=(0,i.useAuth)(),d=(0,l.useRouter)();return((0,a.useEffect)(()=>{if(!o&&t&&!c){let e=window.location.pathname,t=`${s}?redirect=${encodeURIComponent(e)}`;d.push(t)}},[c,o,t,s,d]),o)?r.jsx(n.h2,{message:"Loading..."}):t&&!c?r.jsx(n.h2,{message:"Redirecting..."}):r.jsx(r.Fragment,{children:e})}},5648:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>l,__esModule:()=>a,default:()=>i});let r=(0,t(6843).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\next_js_customer\src\app\profile\page.tsx`),{__esModule:a,$$typeof:l}=r,i=r.default}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[638,265,410,629],()=>t(7441));module.exports=r})();