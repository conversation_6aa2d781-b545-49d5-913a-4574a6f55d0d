(()=>{var e={};e.id=454,e.ids=[454],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},1877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},5319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},3642:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c});var s=r(482),i=r(9108),a=r(2563),n=r.n(a),o=r(8300),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c=["",{children:["auth",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,6852)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\auth\\register\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,1008)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,4117)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,2793)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,8206)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\not-found.tsx"]}],d=["C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\auth\\register\\page.tsx"],u="/auth/register/page",m={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/auth/register/page",pathname:"/auth/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},60:(e,t,r)=>{Promise.resolve().then(r.bind(r,5848))},2254:(e,t,r)=>{e.exports=r(4767)},5848:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var s=r(2295),i=r(3729),a=r(2254),n=r(783),o=r.n(n),l=r(3024),c=r(5814),d=r(1448),u=r(828),m=r(1034),x=r(6548),g=r(8263);function p(){let[e,t]=(0,i.useState)({name:"",mobile_number:"",otp:""}),[r,n]=(0,i.useState)(!1),[p,y]=(0,i.useState)("register"),[b,h]=(0,i.useState)(0),{login:f}=(0,c.useAuth)(),{showToast:v}=(0,d.useToast)(),j=(0,a.useRouter)(),P=(0,a.useSearchParams)();(0,i.useEffect)(()=>{let e=P.get("mobile");e&&t(t=>({...t,mobile_number:e}))},[P]);let _=()=>{h(60);let e=setInterval(()=>{h(t=>t<=1?(clearInterval(e),0):t-1)},1e3)},w=async t=>{if(t.preventDefault(),!e.name||!e.mobile_number){v({type:"error",title:"Please fill all fields"});return}n(!0);try{let t=await x.iJ.registerMobile({mobile_number:e.mobile_number,name:e.name,user_type:"CUSTOMER"});console.log("Registration response:",t);try{await x.iJ.sendOTP(e.mobile_number),console.log("OTP sent successfully after registration")}catch(e){console.log("OTP send after registration failed (might be expected):",e)}y("verify"),_(),v({type:"success",title:"Registration successful! Please verify your mobile number."})}catch(t){console.error("Registration error:",t),(0,g.RZ)(t)?(v({type:"error",title:"Mobile number already registered",message:(0,g.Mo)(t)}),setTimeout(()=>{j.push(`/auth/login?mobile=${encodeURIComponent(e.mobile_number)}&from=register`)},2e3)):(0,g.l8)(t)?v({type:"warning",title:"Too many requests",message:"Too many OTP requests. Please wait for some time and try again."}):v({type:"error",title:"Registration failed",message:(0,g.Mo)(t)})}finally{n(!1)}},N=async()=>{n(!0);try{await x.iJ.resendOTP(e.mobile_number),_(),v({type:"success",title:"OTP resent successfully"})}catch(e){console.error("Resend OTP error:",e),(0,g.l8)(e)?v({type:"warning",title:"Too many requests",message:"Too many OTP requests. Please wait for some time and try again."}):v({type:"error",title:"Failed to resend OTP",message:(0,g.Mo)(e)})}finally{n(!1)}},T=async t=>{if(t.preventDefault(),!e.otp){v({type:"error",title:"Please enter OTP"});return}n(!0);try{let t;console.log("Starting OTP verification for:",e.mobile_number);let r=await x.iJ.verifyOTP(e.mobile_number,e.otp);console.log("OTP verification response:",r);try{t=await x.iJ.loginMobile(e.mobile_number,e.otp),console.log("Login with OTP successful:",t)}catch(t){console.log("Login with OTP failed, trying alternative approach:",t);try{await x.iJ.sendOTP(e.mobile_number),v({type:"info",title:"Account verified!",message:"A new OTP has been sent for login. Please enter the new OTP."});return}catch(e){throw console.error("Failed to send new OTP for login:",e),t}}t&&(f(t),v({type:"success",title:"Account verified and logged in successfully!"}),j.push("/"))}catch(e){console.error("Verification/Login error:",e),(0,g.S9)(e)?v({type:"error",title:"Invalid OTP",message:(0,g.Mo)(e)}):v({type:"error",title:"Verification failed",message:(0,g.Mo)(e)})}finally{n(!1)}};return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[(0,s.jsxs)(o(),{href:"/",className:"flex items-center justify-center mb-6",children:[s.jsx(l.Z,{className:"h-5 w-5 mr-2"}),"Back to Home"]}),s.jsx("div",{className:"flex justify-center mb-6",children:s.jsx("div",{className:"w-12 h-12 bg-primary-600 rounded-lg flex items-center justify-center",children:s.jsx("span",{className:"text-white font-bold text-lg",children:"HS"})})}),s.jsx("h2",{className:"text-center text-3xl font-bold text-gray-900",children:"register"===p?"Create your account":"Verify your mobile number"}),s.jsx("p",{className:"mt-2 text-center text-sm text-gray-600",children:"register"===p?(0,s.jsxs)(s.Fragment,{children:["Already have an account?"," ",s.jsx(o(),{href:"/auth/login",className:"font-medium text-primary-600 hover:text-primary-500",children:"Sign in"})]}):`We've sent an OTP to ${e.mobile_number}`})]}),(0,s.jsxs)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:[s.jsx(m.V,{type:"register"}),s.jsx("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:"register"===p?(0,s.jsxs)("form",{onSubmit:w,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[s.jsx("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700",children:"Full Name"}),s.jsx("div",{className:"mt-1",children:s.jsx("input",{id:"name",name:"name",type:"text",required:!0,value:e.name,onChange:r=>t({...e,name:r.target.value}),placeholder:"Enter your full name",className:"input"})})]}),(0,s.jsxs)("div",{children:[s.jsx("label",{htmlFor:"mobile",className:"block text-sm font-medium text-gray-700",children:"Mobile Number"}),s.jsx("div",{className:"mt-1",children:s.jsx("input",{id:"mobile",name:"mobile",type:"tel",required:!0,value:e.mobile_number,onChange:r=>t({...e,mobile_number:r.target.value}),placeholder:"+91 98765 43210",className:"input"})})]}),s.jsx(u.fl,{type:"submit",isLoading:r,className:"w-full btn-primary",children:"Create Account"})]}):(0,s.jsxs)("form",{onSubmit:T,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[s.jsx("label",{htmlFor:"otp",className:"block text-sm font-medium text-gray-700",children:"Enter OTP"}),s.jsx("div",{className:"mt-1",children:s.jsx("input",{id:"otp",name:"otp",type:"text",required:!0,value:e.otp,onChange:r=>t({...e,otp:r.target.value}),placeholder:"123456",className:"input",maxLength:6})})]}),(0,s.jsxs)("div",{className:"flex space-x-3",children:[s.jsx(u.fl,{type:"submit",isLoading:r,className:"flex-1 btn-primary",children:"Verify & Continue"}),0===b?s.jsx(u.fl,{type:"button",onClick:N,isLoading:r,className:"btn-outline",children:"Resend"}):(0,s.jsxs)("button",{type:"button",disabled:!0,className:"btn-outline opacity-50 cursor-not-allowed",children:[b,"s"]})]}),s.jsx("button",{type:"button",onClick:()=>y("register"),className:"w-full text-sm text-gray-600 hover:text-gray-800",children:"Change mobile number"})]})})]})]})}},1034:(e,t,r)=>{"use strict";r.d(t,{V:()=>c});var s=r(2295),i=r(3729),a=r(2254),n=r(9224);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,n.Z)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]),l=(0,n.Z)("LogIn",[["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}],["polyline",{points:"10 17 15 12 10 7",key:"1ail0h"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}]]),c=({type:e})=>{let[t,r]=(0,i.useState)(!1),[n,c]=(0,i.useState)(""),d=(0,a.useSearchParams)();return((0,i.useEffect)(()=>{let t=d.get("mobile"),s=d.get("from");if(t&&s){r(!0),"register"===e&&"login"===s?c(`Mobile number ${t} is not registered. Please create an account to continue.`):"login"===e&&"register"===s&&c(`Mobile number ${t} is already registered. Please login to continue.`);let i=setTimeout(()=>{r(!1)},1e4);return()=>clearTimeout(i)}},[d,e]),t)?s.jsx("div",{className:"mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[s.jsx("div",{className:"flex-shrink-0",children:"register"===e?s.jsx(o,{className:"h-5 w-5 text-blue-600"}):s.jsx(l,{className:"h-5 w-5 text-blue-600"})}),(0,s.jsxs)("div",{className:"flex-1",children:[s.jsx("h3",{className:"text-sm font-medium text-blue-900 mb-1",children:"register"===e?"Registration Required":"Login Required"}),s.jsx("p",{className:"text-sm text-blue-700",children:n})]}),(0,s.jsxs)("button",{onClick:()=>r(!1),className:"flex-shrink-0 text-blue-400 hover:text-blue-600",children:[s.jsx("span",{className:"sr-only",children:"Close"}),s.jsx("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})]})]})}):null}},8263:(e,t,r)=>{"use strict";r.d(t,{Md:()=>s,Mo:()=>o,RZ:()=>i,S9:()=>a,l8:()=>n});let s=e=>{if(400!==e.status)return!1;let t=e.message?.toLowerCase()||"",r=e.details?.detail?(Array.isArray(e.details.detail)?e.details.detail.join(" "):String(e.details.detail)).toLowerCase():"",s=e.details?.error?(Array.isArray(e.details.error)?e.details.error.join(" "):String(e.details.error)).toLowerCase():"";return["not registered","unregistered","mobile number not found","user not found","does not exist","no account found","please register"].some(e=>t.includes(e)||r.includes(e)||s.includes(e))},i=e=>{if(400!==e.status)return!1;let t=e.message?.toLowerCase()||"",r=e.details?.detail?(Array.isArray(e.details.detail)?e.details.detail.join(" "):String(e.details.detail)).toLowerCase():"",s=e.details?.error?(Array.isArray(e.details.error)?e.details.error.join(" "):String(e.details.error)).toLowerCase():"";return["already registered","already exists","mobile number already in use","user already exists","duplicate"].some(e=>t.includes(e)||r.includes(e)||s.includes(e))},a=e=>{if(400!==e.status)return!1;let t=e.message?.toLowerCase()||"",r=e.details?.detail?(Array.isArray(e.details.detail)?e.details.detail.join(" "):String(e.details.detail)).toLowerCase():"",s=e.details?.error?(Array.isArray(e.details.error)?e.details.error.join(" "):String(e.details.error)).toLowerCase():"";return["invalid otp","incorrect otp","otp expired","otp not found","wrong otp"].some(e=>t.includes(e)||r.includes(e)||s.includes(e))},n=e=>{if(429===e.status)return!0;let t=e.message?e.message.toLowerCase():"",r=e.details?.detail?(Array.isArray(e.details.detail)?e.details.detail.join(" "):String(e.details.detail)).toLowerCase():"",s=e.details?.error?(Array.isArray(e.details.error)?e.details.error.join(" "):String(e.details.error)).toLowerCase():"";return["too many requests","too many otp requests","rate limit","try again later","please wait","request limit exceeded","rate exceeded"].some(e=>t.includes(e)||r.includes(e)||s.includes(e))},o=e=>s(e)?"This mobile number is not registered. You will be redirected to create an account.":i(e)?"This mobile number is already registered. Please login instead.":a(e)?"Invalid or expired OTP. Please try again.":n(e)?"Too many OTP requests. Please wait for some time and try again.":e.message||"An unexpected error occurred. Please try again."},6852:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>a,__esModule:()=>i,default:()=>n});let s=(0,r(6843).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\next_js_customer\src\app\auth\register\page.tsx`),{__esModule:i,$$typeof:a}=s,n=s.default}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,265,410],()=>r(3642));module.exports=s})();