'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeft, Phone } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/components/ui/Toaster';
import { LoadingButton } from '@/components/ui/LoadingSpinner';
import { RedirectMessage } from '@/components/auth/RedirectMessage';
import { authApi } from '@/lib/api';
import { LoginResponse } from '@/types/api';
import { isAlreadyRegisteredError, isInvalidOTPError, isRateLimitError, getUserFriendlyErrorMessage } from '@/lib/errorUtils';

export default function RegisterPage() {
  const [formData, setFormData] = useState({
    name: '',
    mobile_number: '',
    otp: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [step, setStep] = useState<'register' | 'verify'>('register');
  const [otpTimer, setOtpTimer] = useState(0);

  const { login } = useAuth();
  const { showToast } = useToast();
  const router = useRouter();
  const searchParams = useSearchParams();

  // Pre-fill mobile number from URL parameter if available
  useEffect(() => {
    const mobileFromUrl = searchParams.get('mobile');
    if (mobileFromUrl) {
      setFormData(prev => ({ ...prev, mobile_number: mobileFromUrl }));
    }
  }, [searchParams]);

  // Start OTP timer
  const startOtpTimer = () => {
    setOtpTimer(60);
    const interval = setInterval(() => {
      setOtpTimer((prev) => {
        if (prev <= 1) {
          clearInterval(interval);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name || !formData.mobile_number) {
      showToast({ type: 'error', title: 'Please fill all fields' });
      return;
    }

    setIsLoading(true);
    try {
      console.log('Starting registration for:', formData.mobile_number);

      // Register user - Django automatically sends OTP during registration
      const registerResponse = await authApi.registerMobile({
        mobile_number: formData.mobile_number,
        name: formData.name,
        user_type: 'CUSTOMER',
      });

      console.log('Registration successful:', registerResponse);

      // Don't send additional OTP - Django registration already sends it
      setStep('verify');
      startOtpTimer();
      showToast({
        type: 'success',
        title: 'Registration successful!',
        message: 'Please check your mobile for OTP to verify your account.'
      });
    } catch (error: any) {
      console.error('Registration error:', error);

      // Check if the error is due to already registered mobile number
      if (isAlreadyRegisteredError(error)) {
        showToast({
          type: 'info',
          title: 'Mobile number already registered',
          message: 'This mobile number is already registered. Redirecting to login...'
        });
        // Redirect to login page after a short delay
        setTimeout(() => {
          router.push(`/auth/login?mobile=${encodeURIComponent(formData.mobile_number)}&from=register`);
        }, 2000);
      } else if (isRateLimitError(error)) {
        showToast({
          type: 'warning',
          title: 'Too many requests',
          message: 'Too many OTP requests. Please wait for some time and try again.'
        });
      } else {
        showToast({ type: 'error', title: 'Registration failed', message: getUserFriendlyErrorMessage(error) });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendOTP = async () => {
    setIsLoading(true);
    try {
      await authApi.resendOTP(formData.mobile_number);
      startOtpTimer();
      showToast({ type: 'success', title: 'OTP resent successfully' });
    } catch (error: any) {
      console.error('Resend OTP error:', error);
      if (isRateLimitError(error)) {
        showToast({
          type: 'warning',
          title: 'Too many requests',
          message: 'Too many OTP requests. Please wait for some time and try again.'
        });
      } else {
        showToast({ type: 'error', title: 'Failed to resend OTP', message: getUserFriendlyErrorMessage(error) });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyOTP = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.otp) {
      showToast({ type: 'error', title: 'Please enter OTP' });
      return;
    }

    setIsLoading(true);
    try {
      console.log('Starting OTP verification and login for:', formData.mobile_number);

      // Step 1: Verify OTP - this marks user as verified
      const verifyResponse = await authApi.verifyOTP(formData.mobile_number, formData.otp);
      console.log('OTP verification successful:', verifyResponse);

      // Step 2: Immediately try to login with the same OTP
      try {
        console.log('Attempting login with same OTP...');
        const loginResponse = await authApi.loginMobile(formData.mobile_number, formData.otp);
        console.log('Login successful:', loginResponse);

        login(loginResponse as LoginResponse);
        showToast({ type: 'success', title: 'Registration completed successfully! Welcome!' });
        router.push('/');

      } catch (loginError: any) {
        console.log('Login with same OTP failed, this is expected. User is verified but needs to login separately.');

        // User is verified but needs to login through the login page
        showToast({
          type: 'success',
          title: 'Account verified successfully!',
          message: 'Please login with your mobile number to continue.'
        });

        // Redirect to login page with pre-filled mobile number
        setTimeout(() => {
          router.push(`/auth/login?mobile=${encodeURIComponent(formData.mobile_number)}&from=register`);
        }, 2000);
      }

    } catch (error: any) {
      console.error('Verification error:', error);

      if (isInvalidOTPError(error)) {
        showToast({ type: 'error', title: 'Invalid OTP', message: getUserFriendlyErrorMessage(error) });
      } else if (isRateLimitError(error)) {
        showToast({
          type: 'warning',
          title: 'Too many requests',
          message: 'Too many OTP requests. Please wait for some time and try again.'
        });
      } else {
        showToast({ type: 'error', title: 'Verification failed', message: getUserFriendlyErrorMessage(error) });
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <Link href="/" className="flex items-center justify-center mb-6">
          <ArrowLeft className="h-5 w-5 mr-2" />
          Back to Home
        </Link>
        
        <div className="flex justify-center mb-6">
          <div className="w-12 h-12 bg-primary-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-lg">HS</span>
          </div>
        </div>
        
        <h2 className="text-center text-3xl font-bold text-gray-900">
          {step === 'register' ? 'Create your account' : 'Verify your mobile number'}
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          {step === 'register' ? (
            <>
              Already have an account?{' '}
              <Link href="/auth/login" className="font-medium text-primary-600 hover:text-primary-500">
                Sign in
              </Link>
            </>
          ) : (
            `We've sent an OTP to ${formData.mobile_number}. Enter it to complete registration.`
          )}
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <RedirectMessage type="register" />
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          {step === 'register' ? (
            <form onSubmit={handleRegister} className="space-y-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                  Full Name
                </label>
                <div className="mt-1">
                  <input
                    id="name"
                    name="name"
                    type="text"
                    required
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    placeholder="Enter your full name"
                    className="input"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="mobile" className="block text-sm font-medium text-gray-700">
                  Mobile Number
                </label>
                <div className="mt-1">
                  <input
                    id="mobile"
                    name="mobile"
                    type="tel"
                    required
                    value={formData.mobile_number}
                    onChange={(e) => setFormData({ ...formData, mobile_number: e.target.value })}
                    placeholder="+91 98765 43210"
                    className="input"
                  />
                </div>
              </div>

              <LoadingButton
                type="submit"
                isLoading={isLoading}
                className="w-full btn-primary"
              >
                Create Account
              </LoadingButton>
            </form>
          ) : (
            <form onSubmit={handleVerifyOTP} className="space-y-6">
              <div>
                <label htmlFor="otp" className="block text-sm font-medium text-gray-700">
                  Enter OTP
                </label>
                <div className="mt-1">
                  <input
                    id="otp"
                    name="otp"
                    type="text"
                    required
                    value={formData.otp}
                    onChange={(e) => setFormData({ ...formData, otp: e.target.value })}
                    placeholder="123456"
                    className="input"
                    maxLength={6}
                  />
                </div>
              </div>

              <div className="flex space-x-3">
                <LoadingButton
                  type="submit"
                  isLoading={isLoading}
                  className="flex-1 btn-primary"
                >
                  Verify & Complete Registration
                </LoadingButton>

                {otpTimer === 0 ? (
                  <LoadingButton
                    type="button"
                    onClick={handleResendOTP}
                    isLoading={isLoading}
                    className="btn-outline"
                  >
                    Resend
                  </LoadingButton>
                ) : (
                  <button
                    type="button"
                    disabled
                    className="btn-outline opacity-50 cursor-not-allowed"
                  >
                    {otpTimer}s
                  </button>
                )}
              </div>

              <button
                type="button"
                onClick={() => setStep('register')}
                className="w-full text-sm text-gray-600 hover:text-gray-800"
              >
                Change mobile number
              </button>
            </form>
          )}
        </div>
      </div>
    </div>
  );
}
