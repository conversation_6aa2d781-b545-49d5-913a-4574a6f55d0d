'use client';

import React, { useState, useEffect } from 'react';

export default function EnvCheckPage() {
  const [apiStatus, setApiStatus] = useState<'checking' | 'success' | 'error'>('checking');
  const [apiResponse, setApiResponse] = useState<any>(null);
  const [proxyStatus, setProxyStatus] = useState<'checking' | 'success' | 'error'>('checking');
  const [proxyResponse, setProxyResponse] = useState<any>(null);

  useEffect(() => {
    checkApiConnectivity();
  }, []);

  const checkApiConnectivity = async () => {
    // Check direct API connection
    try {
      const directResponse = await fetch('http://localhost:8000/api/catalogue/categories/', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (directResponse.ok) {
        const data = await directResponse.json();
        setApiStatus('success');
        setApiResponse(data);
      } else {
        setApiStatus('error');
        setApiResponse({ error: `HTTP ${directResponse.status}`, statusText: directResponse.statusText });
      }
    } catch (error: any) {
      setApiStatus('error');
      setApiResponse({ error: error.message });
    }

    // Check proxy connection
    try {
      const proxyResponse = await fetch('/api/proxy/catalogue/categories/', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (proxyResponse.ok) {
        const data = await proxyResponse.json();
        setProxyStatus('success');
        setProxyResponse(data);
      } else {
        setProxyStatus('error');
        setProxyResponse({ error: `HTTP ${proxyResponse.status}`, statusText: proxyResponse.statusText });
      }
    } catch (error: any) {
      setProxyStatus('error');
      setProxyResponse({ error: error.message });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'checking': return 'text-yellow-600';
      case 'success': return 'text-green-600';
      case 'error': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'checking': return '⏳';
      case 'success': return '✅';
      case 'error': return '❌';
      default: return '❓';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Environment & API Connectivity Check</h1>
        
        {/* Environment Variables */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Environment Configuration</h2>
          <div className="space-y-2">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <strong>NODE_ENV:</strong> {process.env.NODE_ENV || 'undefined'}
              </div>
              <div>
                <strong>NEXT_PUBLIC_API_BASE_URL:</strong> {process.env.NEXT_PUBLIC_API_BASE_URL || 'undefined'}
              </div>
              <div>
                <strong>Current API Base URL:</strong> {
                  process.env.NODE_ENV === 'development' 
                    ? '/api/proxy' 
                    : (process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000/api')
                }
              </div>
              <div>
                <strong>Expected Django URL:</strong> http://localhost:8000/api
              </div>
            </div>
          </div>
        </div>

        {/* API Connectivity Tests */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Direct API Connection */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              {getStatusIcon(apiStatus)} Direct Django API Connection
              <span className={`ml-2 text-sm ${getStatusColor(apiStatus)}`}>
                ({apiStatus})
              </span>
            </h2>
            <div className="space-y-4">
              <div>
                <strong>Endpoint:</strong> http://localhost:8000/api/catalogue/categories/
              </div>
              <div>
                <strong>Status:</strong> 
                <span className={getStatusColor(apiStatus)}>
                  {apiStatus === 'checking' && ' Checking...'}
                  {apiStatus === 'success' && ' Connected Successfully'}
                  {apiStatus === 'error' && ' Connection Failed'}
                </span>
              </div>
              {apiResponse && (
                <div>
                  <strong>Response:</strong>
                  <pre className="bg-gray-100 p-3 rounded text-sm mt-2 overflow-auto max-h-64">
                    {JSON.stringify(apiResponse, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </div>

          {/* Proxy Connection */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              {getStatusIcon(proxyStatus)} Next.js Proxy Connection
              <span className={`ml-2 text-sm ${getStatusColor(proxyStatus)}`}>
                ({proxyStatus})
              </span>
            </h2>
            <div className="space-y-4">
              <div>
                <strong>Endpoint:</strong> /api/proxy/catalogue/categories/
              </div>
              <div>
                <strong>Status:</strong> 
                <span className={getStatusColor(proxyStatus)}>
                  {proxyStatus === 'checking' && ' Checking...'}
                  {proxyStatus === 'success' && ' Connected Successfully'}
                  {proxyStatus === 'error' && ' Connection Failed'}
                </span>
              </div>
              {proxyResponse && (
                <div>
                  <strong>Response:</strong>
                  <pre className="bg-gray-100 p-3 rounded text-sm mt-2 overflow-auto max-h-64">
                    {JSON.stringify(proxyResponse, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Troubleshooting Guide */}
        <div className="bg-white rounded-lg shadow p-6 mt-8">
          <h2 className="text-xl font-semibold mb-4">Troubleshooting Guide</h2>
          <div className="space-y-4">
            <div>
              <h3 className="font-semibold text-lg">If Direct API Connection Fails:</h3>
              <ul className="list-disc list-inside space-y-1 text-gray-700">
                <li>Make sure Django development server is running on port 8000</li>
                <li>Check if Django CORS settings allow requests from localhost:3000</li>
                <li>Verify Django API endpoints are accessible</li>
                <li>Run: <code className="bg-gray-100 px-2 py-1 rounded">python manage.py runserver 8000</code></li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold text-lg">If Proxy Connection Fails:</h3>
              <ul className="list-disc list-inside space-y-1 text-gray-700">
                <li>Check Next.js proxy configuration in <code className="bg-gray-100 px-2 py-1 rounded">src/app/api/proxy/[...path]/route.ts</code></li>
                <li>Verify the API_BASE_URL in the proxy file points to the correct Django server</li>
                <li>Check browser network tab for detailed error messages</li>
                <li>Restart Next.js development server</li>
              </ul>
            </div>

            <div>
              <h3 className="font-semibold text-lg">Common Issues:</h3>
              <ul className="list-disc list-inside space-y-1 text-gray-700">
                <li><strong>CORS Errors:</strong> Add localhost:3000 to Django CORS_ALLOWED_ORIGINS</li>
                <li><strong>Port Conflicts:</strong> Ensure Django runs on 8000 and Next.js on 3000</li>
                <li><strong>Network Errors:</strong> Check if both servers are running and accessible</li>
                <li><strong>Authentication Issues:</strong> Verify JWT token handling in API requests</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="bg-white rounded-lg shadow p-6 mt-8">
          <h2 className="text-xl font-semibold mb-4">Actions</h2>
          <div className="flex space-x-4">
            <button
              onClick={checkApiConnectivity}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Recheck Connectivity
            </button>
            <a
              href="/debug-api"
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 inline-block"
            >
              Go to API Debug Console
            </a>
            <a
              href="/test-auth"
              className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 inline-block"
            >
              Go to Auth Test Page
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
