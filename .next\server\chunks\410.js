exports.id=410,exports.ids=[410],exports.modules={5095:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2583,23)),Promise.resolve().then(s.t.bind(s,6840,23)),Promise.resolve().then(s.t.bind(s,8771,23)),Promise.resolve().then(s.t.bind(s,3225,23)),Promise.resolve().then(s.t.bind(s,9295,23)),Promise.resolve().then(s.t.bind(s,3982,23))},9779:(e,t,s)=>{Promise.resolve().then(s.bind(s,1448)),Promise.resolve().then(s.bind(s,5814)),Promise.resolve().then(s.bind(s,9200))},1685:(e,t,s)=>{Promise.resolve().then(s.bind(s,6414))},8131:(e,t,s)=>{Promise.resolve().then(s.bind(s,8502))},9559:(e,t,s)=>{Promise.resolve().then(s.bind(s,3932))},6414:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var r=s(2295);s(3729);var a=s(783),o=s.n(a),i=s(5961),n=s(3733),c=s(2086);function l({error:e,reset:t}){return r.jsx("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[r.jsx("div",{className:"flex justify-center mb-6",children:r.jsx("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center",children:r.jsx(i.Z,{className:"h-8 w-8 text-red-600"})})}),r.jsx("h2",{className:"text-center text-3xl font-bold text-gray-900 mb-4",children:"Something went wrong!"}),r.jsx("p",{className:"text-center text-gray-600 mb-8",children:"We encountered an unexpected error. Please try again or contact support if the problem persists."}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("button",{onClick:t,className:"w-full btn-primary flex items-center justify-center",children:[r.jsx(n.Z,{className:"h-4 w-4 mr-2"}),"Try Again"]}),(0,r.jsxs)(o(),{href:"/",className:"w-full btn-secondary flex items-center justify-center",children:[r.jsx(c.Z,{className:"h-4 w-4 mr-2"}),"Go Home"]})]}),!1]})})}},8502:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(2295);s(3729);var a=s(828);function o(){return r.jsx("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center items-center",children:(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("div",{className:"w-12 h-12 bg-primary-600 rounded-lg flex items-center justify-center mb-6 mx-auto",children:r.jsx("span",{className:"text-white font-bold text-lg",children:"HS"})}),r.jsx(a.TK,{size:"lg"}),r.jsx("p",{className:"text-gray-600 mt-4",children:"Loading..."})]})})}},3932:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var r=s(2295);s(3729);var a=s(783),o=s.n(a),i=s(8765),n=s(2086),c=s(3024);function l(){return r.jsx("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md text-center",children:[r.jsx("div",{className:"flex justify-center mb-6",children:r.jsx("div",{className:"w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center",children:r.jsx(i.Z,{className:"h-12 w-12 text-gray-400"})})}),r.jsx("h1",{className:"text-6xl font-bold text-gray-900 mb-4",children:"404"}),r.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Page Not Found"}),r.jsx("p",{className:"text-gray-600 mb-8",children:"The page you're looking for doesn't exist or has been moved."}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)(o(),{href:"/",className:"w-full btn-primary flex items-center justify-center",children:[r.jsx(n.Z,{className:"h-4 w-4 mr-2"}),"Go Home"]}),(0,r.jsxs)("button",{onClick:()=>window.history.back(),className:"w-full btn-secondary flex items-center justify-center",children:[r.jsx(c.Z,{className:"h-4 w-4 mr-2"}),"Go Back"]})]}),r.jsx("div",{className:"mt-8",children:(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["Need help? ",r.jsx(o(),{href:"/contact",className:"text-primary-600 hover:text-primary-700",children:"Contact Support"})]})})]})})}},828:(e,t,s)=>{"use strict";s.d(t,{TK:()=>a,fl:()=>i,h2:()=>o});var r=s(2295);s(3729);let a=({size:e="md",className:t=""})=>r.jsx("div",{className:`${{sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8"}[e]} ${t}`,children:r.jsx("div",{className:"spinner"})}),o=({message:e="Loading..."})=>(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-[400px] space-y-4",children:[r.jsx(a,{size:"lg"}),r.jsx("p",{className:"text-gray-600",children:e})]}),i=({isLoading:e,children:t,className:s="",disabled:o=!1,onClick:i,type:n="button"})=>(0,r.jsxs)("button",{type:n,onClick:i,disabled:o||e,className:`${s} ${e||o?"opacity-50 cursor-not-allowed":""} flex items-center justify-center space-x-2`,children:[e&&r.jsx(a,{size:"sm"}),r.jsx("span",{children:t})]})},1448:(e,t,s)=>{"use strict";s.r(t),s.d(t,{ToastProvider:()=>x,Toaster:()=>y,useToast:()=>m,useToastNotification:()=>g});var r=s(2295),a=s(3729),o=s(7060),i=s(6138),n=s(5961),c=s(1991),l=s(4513);let d=(0,a.createContext)(void 0),m=()=>{let e=(0,a.useContext)(d);if(!e)throw Error("useToast must be used within a ToastProvider");return e},u=({type:e})=>{let t={className:"w-5 h-5"};switch(e){case"success":return r.jsx(o.Z,{...t,className:"w-5 h-5 text-green-500"});case"error":return r.jsx(i.Z,{...t,className:"w-5 h-5 text-red-500"});case"warning":return r.jsx(n.Z,{...t,className:"w-5 h-5 text-yellow-500"});default:return r.jsx(c.Z,{...t,className:"w-5 h-5 text-blue-500"})}},h=({toast:e,onClose:t})=>{let s={success:"bg-green-50 border-green-200",error:"bg-red-50 border-red-200",warning:"bg-yellow-50 border-yellow-200",info:"bg-blue-50 border-blue-200"}[e.type];return r.jsx("div",{className:`${s} border rounded-lg p-4 shadow-lg toast-enter max-w-sm w-full`,children:(0,r.jsxs)("div",{className:"flex items-start",children:[r.jsx("div",{className:"flex-shrink-0",children:r.jsx(u,{type:e.type})}),(0,r.jsxs)("div",{className:"ml-3 flex-1",children:[r.jsx("p",{className:"text-sm font-medium text-gray-900",children:e.title}),e.message&&r.jsx("p",{className:"mt-1 text-sm text-gray-600",children:e.message})]}),r.jsx("div",{className:"ml-4 flex-shrink-0",children:r.jsx("button",{onClick:()=>t(e.id),className:"inline-flex text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500",children:r.jsx(l.Z,{className:"w-4 h-4"})})})]})})},x=({children:e})=>{let[t,s]=(0,a.useState)([]),o=(0,a.useCallback)(e=>{let t=Math.random().toString(36).substr(2,9),r={...e,id:t};s(e=>[...e,r]),setTimeout(()=>{s(e=>e.filter(e=>e.id!==t))},e.duration||5e3)},[]),i=(0,a.useCallback)(e=>{s(t=>t.filter(t=>t.id!==e))},[]);return(0,r.jsxs)(d.Provider,{value:{showToast:o,hideToast:i},children:[e,r.jsx("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:t.map(e=>r.jsx(h,{toast:e,onClose:i},e.id))})]})},y=x,g=()=>{let{showToast:e}=m();return{success:(t,s)=>e({type:"success",title:t,message:s}),error:(t,s)=>e({type:"error",title:t,message:s}),info:(t,s)=>e({type:"info",title:t,message:s}),warning:(t,s)=>e({type:"warning",title:t,message:s})}}},5814:(e,t,s)=>{"use strict";s.r(t),s.d(t,{AuthProvider:()=>c,useAuth:()=>n});var r=s(2295),a=s(3729),o=s(6548);let i=(0,a.createContext)(void 0),n=()=>{let e=(0,a.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},c=({children:e})=>{let[t,s]=(0,a.useState)(null),[n,c]=(0,a.useState)(!0),l=!!t;(0,a.useEffect)(()=>{(async()=>{let{refresh:e}=(0,o.lz)();if(e)try{let e=await o.iJ.getProfile();s(e)}catch(e){(0,o.yE)()}c(!1)})()},[]);let d=async()=>{try{await o.iJ.logout()}catch(e){console.error("Logout error:",e)}finally{(0,o.yE)(),s(null)}},m=async()=>{if(l)try{let e=await o.iJ.getProfile();s(e)}catch(e){console.error("Failed to refresh user profile:",e)}};return r.jsx(i.Provider,{value:{user:t,isAuthenticated:l,isLoading:n,login:e=>{(0,o.d0)(e.tokens),s(e.user)},logout:d,updateUser:e=>{t&&s({...t,...e})},refreshUserProfile:m},children:e})}},9200:(e,t,s)=>{"use strict";s.r(t),s.d(t,{CartProvider:()=>l,useCart:()=>c});var r=s(2295),a=s(3729),o=s(6548),i=s(5814);let n=(0,a.createContext)(void 0),c=()=>{let e=(0,a.useContext)(n);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e},l=({children:e})=>{let[t,s]=(0,a.useState)(null),[c,l]=(0,a.useState)(null),[d,m]=(0,a.useState)(!1),{isAuthenticated:u}=(0,i.useAuth)(),h=async()=>{m(!0);try{console.log("Refreshing cart data, authenticated:",u);let[e,t]=await Promise.all([o.wk.getCart(),o.wk.getCartSummary()]);console.log("Cart data received:",e),console.log("Cart summary received:",t),s(e),l(t)}catch(e){console.error("Failed to fetch cart:",e),401===e.status?console.log("Cart fetch failed due to authentication - user might need to login"):404===e.status&&console.log("Cart not found - initializing empty cart"),s(null),l(null)}finally{m(!1)}};(0,a.useEffect)(()=>{h()},[u]);let x=async(e,t=1)=>{try{console.log("Adding to cart:",e.id,"quantity:",t,"authenticated:",u);let s=await o.wk.addToCart(e.id,t);console.log("Add to cart response:",s),await h()}catch(e){if(console.error("Failed to add to cart:",e),401===e.status)throw Error("Please login to add items to cart");if(404===e.status)throw Error("Service not found");if(400===e.status)throw Error(e.message||"Invalid request");throw Error("Failed to add item to cart. Please try again.")}},y=async(e,t)=>{try{await o.wk.updateCartItem(e,t),await h()}catch(e){throw console.error("Failed to update cart item:",e),e}},g=async e=>{try{await o.wk.removeCartItem(e),await h()}catch(e){throw console.error("Failed to remove cart item:",e),e}},f=async()=>{try{await o.wk.clearCart(),await h()}catch(e){throw console.error("Failed to clear cart:",e),e}},p=async e=>{try{await o.wk.applyCoupon(e),await h()}catch(e){throw console.error("Failed to apply coupon:",e),e}},v=async()=>{try{await o.wk.removeCoupon(),await h()}catch(e){throw console.error("Failed to remove coupon:",e),e}};return r.jsx(n.Provider,{value:{cart:t,cartSummary:c,isLoading:d,addToCart:x,updateCartItem:y,removeCartItem:g,clearCart:f,applyCoupon:p,removeCoupon:v,refreshCart:h,getTotalItems:()=>c?.items_count||0},children:e})}},6548:(e,t,s)=>{"use strict";s.d(t,{Eh:()=>x,Yw:()=>y,d0:()=>i,iJ:()=>m,jv:()=>u,lz:()=>n,wk:()=>h,yE:()=>c});let r="http://localhost:8000/api",a=null,o=null,i=e=>{a=e.access,o=e.refresh},n=()=>({access:a,refresh:o}),c=()=>{a=null,o=null},l=async()=>{let{refresh:e}=n();if(!e)return!1;try{let t=await fetch(`${r}/auth/token/refresh/`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refresh:e})});if(!t.ok)return c(),!1;{let e=await t.json();return i(e),!0}}catch(e){return c(),!1}},d=async(e,t={})=>{let s=e.startsWith("http")?e:`${r}${e}`,{access:a}=n(),i={"Content-Type":"application/json",...t.headers};a&&(i.Authorization=`Bearer ${a}`),console.log(`API Request: ${t.method||"GET"} ${s}`),t.body&&console.log("Request body:",t.body);let c=await fetch(s,{...t,headers:i});if(console.log(`API Response: ${c.status} ${c.statusText}`),401===c.status&&o){if(console.log("Attempting token refresh..."),await l()){console.log("Token refreshed successfully, retrying request...");let{access:e}=n();i.Authorization=`Bearer ${e}`,c=await fetch(s,{...t,headers:i}),console.log(`Retry API Response: ${c.status} ${c.statusText}`)}else console.log("Token refresh failed")}if(!c.ok){let e;try{e=await c.json()}catch(t){e={error:c.statusText}}throw console.error("API Error:",c.status,e),{message:e.message||e.detail||e.error||`HTTP ${c.status}`,details:e,status:c.status}}if(204===c.status)return{};let d=await c.json();return console.log("API Response data:",d),d},m={registerMobile:e=>d("/auth/register/mobile/",{method:"POST",body:JSON.stringify(e)}),sendOTP:e=>d("/auth/otp/send/",{method:"POST",body:JSON.stringify({mobile_number:e})}),resendOTP:e=>d("/auth/otp/resend/",{method:"POST",body:JSON.stringify({mobile_number:e})}),verifyOTP:(e,t)=>d("/auth/otp/verify/",{method:"POST",body:JSON.stringify({mobile_number:e,otp:t})}),loginMobile:(e,t)=>d("/auth/login/mobile/",{method:"POST",body:JSON.stringify({mobile_number:e,otp:t})}),loginEmail:(e,t)=>d("/auth/login/email/",{method:"POST",body:JSON.stringify({email:e,password:t})}),getProfile:()=>d("/auth/profile/"),updateProfile:e=>d("/auth/profile/",{method:"PUT",body:JSON.stringify(e)}),logout:()=>{let{refresh:e}=n();return d("/auth/logout/",{method:"POST",body:JSON.stringify({refresh:e})})},getAddresses:()=>d("/auth/addresses/"),createAddress:e=>d("/auth/addresses/",{method:"POST",body:JSON.stringify(e)}),updateAddress:(e,t)=>d(`/auth/addresses/${e}/`,{method:"PUT",body:JSON.stringify(t)}),deleteAddress:e=>d(`/auth/addresses/${e}/`,{method:"DELETE"})},u={getCategories:e=>{let t=e?`?${new URLSearchParams(e).toString()}`:"";return d(`/catalogue/categories/${t}`)},getCategoryDetail:e=>d(`/catalogue/categories/${e}/`),getCategoryTree:()=>d("/catalogue/categories/tree/"),getCategoryServices:e=>d(`/catalogue/categories/${e}/services/`),getServices:e=>{let t=e?`?${new URLSearchParams(e).toString()}`:"";return d(`/catalogue/services/${t}`)},getServiceDetail:e=>d(`/catalogue/services/${e}/`),searchServices:e=>{let t=new URLSearchParams(e).toString();return d(`/catalogue/services/search/?${t}`)}},h={getCart:()=>d("/cart/"),getCartSummary:()=>d("/cart/summary/"),addToCart:(e,t)=>d("/cart/add/",{method:"POST",body:JSON.stringify({service:e,quantity:t})}),updateCartItem:(e,t)=>d(`/cart/items/${e}/update/`,{method:"PUT",body:JSON.stringify({quantity:t})}),removeCartItem:e=>d(`/cart/items/${e}/remove/`,{method:"DELETE"}),clearCart:()=>d("/cart/clear/",{method:"POST"}),applyCoupon:e=>d("/cart/coupon/apply/",{method:"POST",body:JSON.stringify({coupon_code:e})}),removeCoupon:()=>d("/cart/coupon/remove/",{method:"POST"})},x={getOrders:()=>d("/orders/"),getOrderDetail:e=>d(`/orders/${e}/`),createOrder:e=>d("/orders/",{method:"POST",body:JSON.stringify(e)}),cancelOrder:(e,t)=>d(`/orders/${e}/cancel/`,{method:"POST",body:JSON.stringify({cancellation_reason:t})})},y={initiatePayment:e=>d("/payments/initiate/",{method:"POST",body:JSON.stringify(e)}),handleRazorpayCallback:e=>d("/payments/razorpay/callback/",{method:"POST",body:JSON.stringify(e)}),getPaymentStatus:e=>d(`/payments/status/${e}/`)}},4117:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>o,__esModule:()=>a,default:()=>i});let r=(0,s(6843).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\next_js_customer\src\app\error.tsx`),{__esModule:a,$$typeof:o}=r,i=r.default},1008:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>j,metadata:()=>b,viewport:()=>v});var r=s(5036),a=s(3640),o=s.n(a);s(5023);var i=s(6843);let n=(0,i.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\next_js_customer\src\contexts\AuthContext.tsx`),{__esModule:c,$$typeof:l}=n;n.default,(0,i.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\next_js_customer\src\contexts\AuthContext.tsx#useAuth`);let d=(0,i.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\next_js_customer\src\contexts\AuthContext.tsx#AuthProvider`),m=(0,i.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\next_js_customer\src\contexts\CartContext.tsx`),{__esModule:u,$$typeof:h}=m;m.default,(0,i.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\next_js_customer\src\contexts\CartContext.tsx#useCart`);let x=(0,i.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\next_js_customer\src\contexts\CartContext.tsx#CartProvider`),y=(0,i.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\next_js_customer\src\components\ui\Toaster.tsx`),{__esModule:g,$$typeof:f}=y;y.default,(0,i.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\next_js_customer\src\components\ui\Toaster.tsx#useToast`);let p=(0,i.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\next_js_customer\src\components\ui\Toaster.tsx#ToastProvider`);(0,i.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\next_js_customer\src\components\ui\Toaster.tsx#Toaster`),(0,i.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\next_js_customer\src\components\ui\Toaster.tsx#useToastNotification`);let v={width:"device-width",initialScale:1,maximumScale:1,themeColor:"#3b82f6"},b={metadataBase:new URL("http://localhost:3000"),title:"Home Services - Professional Services at Your Doorstep",description:"Book professional home services including cleaning, plumbing, electrical work, and more. Fast, reliable, and affordable services.",keywords:"home services, cleaning, plumbing, electrical, repair, maintenance, professional services",authors:[{name:"Home Services Team"}],creator:"Home Services",publisher:"Home Services",formatDetection:{email:!1,address:!1,telephone:!1},manifest:"/manifest.json",appleWebApp:{capable:!0,statusBarStyle:"default",title:"Home Services"},openGraph:{type:"website",siteName:"Home Services",title:"Home Services - Professional Services at Your Doorstep",description:"Book professional home services including cleaning, plumbing, electrical work, and more.",images:[{url:"/og-image.jpg",width:1200,height:630,alt:"Home Services"}]},twitter:{card:"summary_large_image",title:"Home Services - Professional Services at Your Doorstep",description:"Book professional home services including cleaning, plumbing, electrical work, and more.",images:["/og-image.jpg"]}};function j({children:e}){return(0,r.jsxs)("html",{lang:"en",children:[(0,r.jsxs)("head",{children:[r.jsx("link",{rel:"icon",href:"/favicon.ico"}),r.jsx("link",{rel:"apple-touch-icon",href:"/icons/icon-192x192.png"}),r.jsx("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),r.jsx("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"}),r.jsx("meta",{name:"apple-mobile-web-app-title",content:"Home Services"}),r.jsx("meta",{name:"mobile-web-app-capable",content:"yes"}),r.jsx("meta",{name:"msapplication-TileColor",content:"#3b82f6"}),r.jsx("meta",{name:"msapplication-tap-highlight",content:"no"})]}),r.jsx("body",{className:o().className,children:r.jsx(p,{children:r.jsx(d,{children:r.jsx(x,{children:r.jsx("div",{className:"min-h-screen bg-gray-50",children:e})})})})})]})}},2793:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>o,__esModule:()=>a,default:()=>i});let r=(0,s(6843).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\next_js_customer\src\app\loading.tsx`),{__esModule:a,$$typeof:o}=r,i=r.default},8206:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>o,__esModule:()=>a,default:()=>i});let r=(0,s(6843).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\next_js_customer\src\app\not-found.tsx`),{__esModule:a,$$typeof:o}=r,i=r.default},5023:()=>{}};