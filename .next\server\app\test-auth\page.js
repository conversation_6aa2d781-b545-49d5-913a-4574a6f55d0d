(()=>{var e={};e.id=212,e.ids=[212],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},1877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},5319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},1308:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>a.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>b,tree:()=>d});var r=t(482),i=t(9108),o=t(2563),a=t.n(o),l=t(8300),n={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);t.d(s,n);let d=["",{children:["test-auth",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1329)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\test-auth\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,1008)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,4117)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,2793)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,8206)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\not-found.tsx"]}],c=["C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\test-auth\\page.tsx"],m="/test-auth/page",u={require:t,loadChunk:()=>Promise.resolve()},b=new r.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/test-auth/page",pathname:"/test-auth",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3436:(e,s,t)=>{Promise.resolve().then(t.bind(t,9206))},9206:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var r=t(2295),i=t(3729),o=t(6548),a=t(5814);function l(){let[e,s]=(0,i.useState)({}),[t,l]=(0,i.useState)({}),[n,d]=(0,i.useState)({mobile_number:"+919876543210",name:"Test Customer",otp:"123456"}),{user:c,login:m,logout:u}=(0,a.useAuth)(),b=(e,t)=>{s(s=>({...s,[e]:t}))},g=(e,s)=>{l(t=>({...t,[e]:s}))},x=async()=>{let e="registerMobile";g(e,!0);try{let s=await o.iJ.registerMobile({mobile_number:n.mobile_number,name:n.name,user_type:"CUSTOMER"});b(e,{success:!0,data:s})}catch(s){b(e,{success:!1,error:s.message,details:s.details})}finally{g(e,!1)}},p=async()=>{let e="sendOTP";g(e,!0);try{let s=await o.iJ.sendOTP(n.mobile_number);b(e,{success:!0,data:s})}catch(s){b(e,{success:!1,error:s.message,details:s.details})}finally{g(e,!1)}},h=async()=>{let e="resendOTP";g(e,!0);try{let s=await o.iJ.resendOTP(n.mobile_number);b(e,{success:!0,data:s})}catch(s){b(e,{success:!1,error:s.message,details:s.details})}finally{g(e,!1)}},y=async()=>{let e="verifyOTP";g(e,!0);try{let s=await o.iJ.verifyOTP(n.mobile_number,n.otp);b(e,{success:!0,data:s})}catch(s){b(e,{success:!1,error:s.message,details:s.details})}finally{g(e,!1)}},v=async()=>{let e="loginMobile";g(e,!0);try{let s=await o.iJ.loginMobile(n.mobile_number,n.otp);b(e,{success:!0,data:s}),s&&m(s)}catch(s){b(e,{success:!1,error:s.message,details:s.details})}finally{g(e,!1)}},j=async()=>{let e="getProfile";g(e,!0);try{let s=await o.iJ.getProfile();b(e,{success:!0,data:s})}catch(s){b(e,{success:!1,error:s.message,details:s.details})}finally{g(e,!1)}},f=async()=>{let e="logout";g(e,!0);try{await u(),b(e,{success:!0,message:"Logged out successfully"})}catch(s){b(e,{success:!1,error:s.message,details:s.details})}finally{g(e,!1)}},P=async()=>{let e="completeRegistrationFlow";g(e,!0);try{let s=await o.iJ.registerMobile({mobile_number:n.mobile_number,name:n.name,user_type:"CUSTOMER"}),t=await o.iJ.sendOTP(n.mobile_number),r=await o.iJ.verifyOTP(n.mobile_number,n.otp),i=await o.iJ.loginMobile(n.mobile_number,n.otp);m(i),b(e,{success:!0,data:{register:s,sendOTP:t,verifyOTP:r,login:i,message:"Complete registration flow successful with JWT tokens!"}})}catch(s){b(e,{success:!1,error:s.message,details:s.details})}finally{g(e,!1)}};return r.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4",children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-8",children:"Authentication API Test"}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-8",children:[r.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Current User Status"}),c?(0,r.jsxs)("div",{className:"text-green-600",children:[(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"Logged in as:"})," ",c.name]}),(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"Mobile:"})," ",c.mobile_number]}),(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"User Type:"})," ",c.user_type]}),(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"Verified:"})," ",c.is_verified?"Yes":"No"]})]}):r.jsx("p",{className:"text-gray-600",children:"Not logged in"})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-8",children:[r.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Test Data"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Mobile Number"}),r.jsx("input",{type:"tel",value:n.mobile_number,onChange:e=>d(s=>({...s,mobile_number:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Name"}),r.jsx("input",{type:"text",value:n.name,onChange:e=>d(s=>({...s,name:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"OTP"}),r.jsx("input",{type:"text",value:n.otp,onChange:e=>d(s=>({...s,otp:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",maxLength:6})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-8",children:[r.jsx("h2",{className:"text-xl font-semibold mb-4",children:"API Tests"}),(0,r.jsxs)("div",{className:"mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200",children:[r.jsx("h3",{className:"font-semibold text-blue-800 mb-2",children:"Complete Registration Flow (Recommended)"}),r.jsx("p",{className:"text-sm text-blue-600 mb-3",children:"This tests the complete registration flow: Register → Send OTP → Verify OTP → Auto Login with JWT tokens"}),r.jsx("button",{onClick:P,disabled:t.completeRegistrationFlow,className:"px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 font-semibold",children:t.completeRegistrationFlow?"Running Complete Flow...":"Test Complete Registration Flow"})]}),r.jsx("h3",{className:"font-medium text-gray-700 mb-3",children:"Individual API Tests"}),(0,r.jsxs)("p",{className:"text-sm text-gray-600 mb-4",children:[r.jsx("strong",{children:"Note:"}),' Individual "Verify OTP" test only verifies the OTP but doesn\'t return JWT tokens. Use "Login Mobile" after verification, or use the complete flow above.']}),(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[r.jsx("button",{onClick:x,disabled:t.registerMobile,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50",children:t.registerMobile?"Loading...":"Register Mobile"}),r.jsx("button",{onClick:p,disabled:t.sendOTP,className:"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50",children:t.sendOTP?"Loading...":"Send OTP"}),r.jsx("button",{onClick:h,disabled:t.resendOTP,className:"px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 disabled:opacity-50",children:t.resendOTP?"Loading...":"Resend OTP"}),r.jsx("button",{onClick:y,disabled:t.verifyOTP,className:"px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50",children:t.verifyOTP?"Loading...":"Verify OTP"}),r.jsx("button",{onClick:v,disabled:t.loginMobile,className:"px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50",children:t.loginMobile?"Loading...":"Login Mobile"}),r.jsx("button",{onClick:j,disabled:t.getProfile,className:"px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 disabled:opacity-50",children:t.getProfile?"Loading...":"Get Profile"}),r.jsx("button",{onClick:f,disabled:t.logout,className:"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50",children:t.logout?"Loading...":"Logout"}),r.jsx("button",{onClick:()=>{s({})},className:"px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700",children:"Clear Results"})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[r.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Test Results"}),0===Object.keys(e).length?r.jsx("p",{className:"text-gray-600",children:"No test results yet. Click the buttons above to test the APIs."}):r.jsx("div",{className:"space-y-4",children:Object.entries(e).map(([e,s])=>(0,r.jsxs)("div",{className:"border rounded-lg p-4",children:[r.jsx("h3",{className:"font-semibold text-lg mb-2 capitalize",children:e}),(0,r.jsxs)("div",{className:`p-3 rounded ${s.success?"bg-green-50 border border-green-200":"bg-red-50 border border-red-200"}`,children:[r.jsx("div",{className:`font-medium ${s.success?"text-green-800":"text-red-800"}`,children:s.success?"✅ Success":"❌ Error"}),r.jsx("pre",{className:"mt-2 text-sm overflow-x-auto",children:JSON.stringify(s,null,2)})]})]},e))})]})]})})}},1329:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>o,__esModule:()=>i,default:()=>a});let r=(0,t(6843).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\next_js_customer\src\app\test-auth\page.tsx`),{__esModule:i,$$typeof:o}=r,a=r.default}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[638,265,410],()=>t(1308));module.exports=r})();