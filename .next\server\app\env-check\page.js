(()=>{var e={};e.id=655,e.ids=[655],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},1877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},5319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},3469:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>c.a,__next_app__:()=>h,originalPathname:()=>x,pages:()=>d,routeModule:()=>u,tree:()=>l});var t=r(482),n=r(9108),i=r(2563),c=r.n(i),o=r(8300),a={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>o[e]);r.d(s,a);let l=["",{children:["env-check",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,6534)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\env-check\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,1008)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,4117)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,2793)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,8206)),"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\not-found.tsx"]}],d=["C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\next_js_customer\\src\\app\\env-check\\page.tsx"],x="/env-check/page",h={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/env-check/page",pathname:"/env-check",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},5873:(e,s,r)=>{Promise.resolve().then(r.bind(r,6725))},6725:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>i});var t=r(2295),n=r(3729);function i(){let[e,s]=(0,n.useState)("checking"),[r,i]=(0,n.useState)(null),[c,o]=(0,n.useState)("checking"),[a,l]=(0,n.useState)(null);(0,n.useEffect)(()=>{d()},[]);let d=async()=>{try{let e=await fetch("http://localhost:8000/api/catalogue/categories/",{method:"GET",headers:{"Content-Type":"application/json"}});if(e.ok){let r=await e.json();s("success"),i(r)}else s("error"),i({error:`HTTP ${e.status}`,statusText:e.statusText})}catch(e){s("error"),i({error:e.message})}try{let e=await fetch("/api/proxy/catalogue/categories/",{method:"GET",headers:{"Content-Type":"application/json"}});if(e.ok){let s=await e.json();o("success"),l(s)}else o("error"),l({error:`HTTP ${e.status}`,statusText:e.statusText})}catch(e){o("error"),l({error:e.message})}},x=e=>{switch(e){case"checking":return"text-yellow-600";case"success":return"text-green-600";case"error":return"text-red-600";default:return"text-gray-600"}},h=e=>{switch(e){case"checking":return"⏳";case"success":return"✅";case"error":return"❌";default:return"❓"}};return t.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto px-4",children:[t.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-8",children:"Environment & API Connectivity Check"}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-8",children:[t.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Environment Configuration"}),t.jsx("div",{className:"space-y-2",children:(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[t.jsx("strong",{children:"NODE_ENV:"})," ","production"]}),(0,t.jsxs)("div",{children:[t.jsx("strong",{children:"NEXT_PUBLIC_API_BASE_URL:"})," ","http://localhost:8000/api"]}),(0,t.jsxs)("div",{children:[t.jsx("strong",{children:"Current API Base URL:"})," ","http://localhost:8000/api"]}),(0,t.jsxs)("div",{children:[t.jsx("strong",{children:"Expected Django URL:"})," http://localhost:8000/api"]})]})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,t.jsxs)("h2",{className:"text-xl font-semibold mb-4 flex items-center",children:[h(e)," Direct Django API Connection",(0,t.jsxs)("span",{className:`ml-2 text-sm ${x(e)}`,children:["(",e,")"]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[t.jsx("strong",{children:"Endpoint:"})," http://localhost:8000/api/catalogue/categories/"]}),(0,t.jsxs)("div",{children:[t.jsx("strong",{children:"Status:"}),(0,t.jsxs)("span",{className:x(e),children:["checking"===e&&" Checking...","success"===e&&" Connected Successfully","error"===e&&" Connection Failed"]})]}),r&&(0,t.jsxs)("div",{children:[t.jsx("strong",{children:"Response:"}),t.jsx("pre",{className:"bg-gray-100 p-3 rounded text-sm mt-2 overflow-auto max-h-64",children:JSON.stringify(r,null,2)})]})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,t.jsxs)("h2",{className:"text-xl font-semibold mb-4 flex items-center",children:[h(c)," Next.js Proxy Connection",(0,t.jsxs)("span",{className:`ml-2 text-sm ${x(c)}`,children:["(",c,")"]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[t.jsx("strong",{children:"Endpoint:"})," /api/proxy/catalogue/categories/"]}),(0,t.jsxs)("div",{children:[t.jsx("strong",{children:"Status:"}),(0,t.jsxs)("span",{className:x(c),children:["checking"===c&&" Checking...","success"===c&&" Connected Successfully","error"===c&&" Connection Failed"]})]}),a&&(0,t.jsxs)("div",{children:[t.jsx("strong",{children:"Response:"}),t.jsx("pre",{className:"bg-gray-100 p-3 rounded text-sm mt-2 overflow-auto max-h-64",children:JSON.stringify(a,null,2)})]})]})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mt-8",children:[t.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Troubleshooting Guide"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"font-semibold text-lg",children:"If Direct API Connection Fails:"}),(0,t.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-gray-700",children:[t.jsx("li",{children:"Make sure Django development server is running on port 8000"}),t.jsx("li",{children:"Check if Django CORS settings allow requests from localhost:3000"}),t.jsx("li",{children:"Verify Django API endpoints are accessible"}),(0,t.jsxs)("li",{children:["Run: ",t.jsx("code",{className:"bg-gray-100 px-2 py-1 rounded",children:"python manage.py runserver 8000"})]})]})]}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"font-semibold text-lg",children:"If Proxy Connection Fails:"}),(0,t.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-gray-700",children:[(0,t.jsxs)("li",{children:["Check Next.js proxy configuration in ",t.jsx("code",{className:"bg-gray-100 px-2 py-1 rounded",children:"src/app/api/proxy/[...path]/route.ts"})]}),t.jsx("li",{children:"Verify the API_BASE_URL in the proxy file points to the correct Django server"}),t.jsx("li",{children:"Check browser network tab for detailed error messages"}),t.jsx("li",{children:"Restart Next.js development server"})]})]}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"font-semibold text-lg",children:"Common Issues:"}),(0,t.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-gray-700",children:[(0,t.jsxs)("li",{children:[t.jsx("strong",{children:"CORS Errors:"})," Add localhost:3000 to Django CORS_ALLOWED_ORIGINS"]}),(0,t.jsxs)("li",{children:[t.jsx("strong",{children:"Port Conflicts:"})," Ensure Django runs on 8000 and Next.js on 3000"]}),(0,t.jsxs)("li",{children:[t.jsx("strong",{children:"Network Errors:"})," Check if both servers are running and accessible"]}),(0,t.jsxs)("li",{children:[t.jsx("strong",{children:"Authentication Issues:"})," Verify JWT token handling in API requests"]})]})]})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mt-8",children:[t.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Actions"}),(0,t.jsxs)("div",{className:"flex space-x-4",children:[t.jsx("button",{onClick:d,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Recheck Connectivity"}),t.jsx("a",{href:"/debug-api",className:"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 inline-block",children:"Go to API Debug Console"}),t.jsx("a",{href:"/test-auth",className:"px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 inline-block",children:"Go to Auth Test Page"})]})]})]})})}},6534:(e,s,r)=>{"use strict";r.r(s),r.d(s,{$$typeof:()=>i,__esModule:()=>n,default:()=>c});let t=(0,r(6843).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\next_js_customer\src\app\env-check\page.tsx`),{__esModule:n,$$typeof:i}=t,c=t.default}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[638,265,410],()=>r(3469));module.exports=t})();